#!/usr/bin/env python3
"""
Test script for the enhanced WhisperWriter features.
This script tests the new UI components and functionality.
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PyQt5.QtCore import QTimer

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from utils import ConfigManager
from ui.enhanced_status_window import EnhancedStatusWindow, VoiceLevelMeter


class TestWindow(QMainWindow):
    """Test window for enhanced features."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Enhanced WhisperWriter Features Test")
        self.setGeometry(100, 100, 400, 300)
        
        # Initialize ConfigManager
        ConfigManager.initialize()
        
        # Create main widget
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QVBoxLayout(main_widget)
        
        # Test buttons
        self.create_test_buttons(layout)
        
        # Enhanced status window
        self.enhanced_status = None
        
        # Voice level test timer
        self.voice_timer = QTimer()
        self.voice_timer.timeout.connect(self.update_voice_level)
        self.voice_level = 0
        self.voice_direction = 1
        
    def create_test_buttons(self, layout):
        """Create test buttons."""
        layout.addWidget(QLabel("Enhanced WhisperWriter Features Test"))
        
        # Test audio devices
        devices_btn = QPushButton("Test Audio Device Detection")
        devices_btn.clicked.connect(self.test_audio_devices)
        layout.addWidget(devices_btn)
        
        # Test enhanced status window
        status_btn = QPushButton("Test Enhanced Status Window")
        status_btn.clicked.connect(self.test_enhanced_status)
        layout.addWidget(status_btn)
        
        # Test voice level meter
        voice_btn = QPushButton("Test Voice Level Meter")
        voice_btn.clicked.connect(self.test_voice_meter)
        layout.addWidget(voice_btn)
        
        # Test recording history
        history_btn = QPushButton("Test Recording History")
        history_btn.clicked.connect(self.test_recording_history)
        layout.addWidget(history_btn)
        
        # Close button
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
        
    def test_audio_devices(self):
        """Test audio device detection."""
        print("Testing audio device detection...")
        devices = ConfigManager.get_audio_devices()
        device_names = ConfigManager.get_audio_device_names()
        
        print(f"Found {len(devices)} audio input devices:")
        for device in devices:
            print(f"  {device['index']}: {device['name']} ({device['channels']} channels)")
            
        print(f"\nDevice names for dropdown: {device_names}")
        
        # Test device selection parsing
        test_selections = ["Default", "0: Built-in Microphone", "1: USB Microphone"]
        for selection in test_selections:
            parsed = ConfigManager.parse_device_selection(selection)
            print(f"  '{selection}' -> {parsed}")
            
    def test_enhanced_status(self):
        """Test enhanced status window."""
        print("Testing enhanced status window...")
        if self.enhanced_status is None:
            self.enhanced_status = EnhancedStatusWindow()
            
        self.enhanced_status.show()
        
        # Simulate status changes
        QTimer.singleShot(1000, lambda: self.enhanced_status.update_status('recording'))
        QTimer.singleShot(3000, lambda: self.enhanced_status.update_status('transcribing'))
        QTimer.singleShot(5000, lambda: self.enhanced_status.update_status('idle'))
        
    def test_voice_meter(self):
        """Test voice level meter."""
        print("Testing voice level meter...")
        if self.enhanced_status is None:
            self.enhanced_status = EnhancedStatusWindow()
            
        self.enhanced_status.show()
        
        # Start voice level animation
        self.voice_timer.start(100)
        
        # Stop after 10 seconds
        QTimer.singleShot(10000, self.voice_timer.stop)
        
    def update_voice_level(self):
        """Update voice level for testing."""
        if self.enhanced_status:
            self.enhanced_status.update_voice_level(self.voice_level)
            
        # Animate voice level
        self.voice_level += self.voice_direction * 5
        if self.voice_level >= 100:
            self.voice_direction = -1
        elif self.voice_level <= 0:
            self.voice_direction = 1
            
    def test_recording_history(self):
        """Test recording history functionality."""
        print("Testing recording history...")
        if self.enhanced_status is None:
            self.enhanced_status = EnhancedStatusWindow()
            
        self.enhanced_status.show()
        
        # Add some test recordings
        test_recordings = [
            "This is a test transcription for the first recording.",
            "Another test transcription with some longer text to see how it displays.",
            "Short test.",
            "A fourth test transcription to check the history scrolling functionality."
        ]
        
        for i, text in enumerate(test_recordings):
            QTimer.singleShot(i * 1000, lambda t=text: self.enhanced_status.add_recording_to_history(t))


def main():
    """Main function."""
    app = QApplication(sys.argv)
    
    # Create test window
    test_window = TestWindow()
    test_window.show()
    
    print("Enhanced WhisperWriter Features Test")
    print("====================================")
    print("Click the buttons to test different features:")
    print("1. Audio Device Detection - Tests device enumeration")
    print("2. Enhanced Status Window - Tests the new UI")
    print("3. Voice Level Meter - Tests animated voice bars")
    print("4. Recording History - Tests history functionality")
    print()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
