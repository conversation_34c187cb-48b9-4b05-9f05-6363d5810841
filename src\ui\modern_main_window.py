import sys
import os
from PyQt5.QtCore import (Qt, pyqtSignal, QTimer, QSize)
from PyQt5.QtGui import (QFont, QIcon, QPixmap, QPainter, QBrush, QColor, 
                        QLinearGradient, QFontMetrics)
from PyQt5.QtWidgets import (QApplication, QLabel, QHBoxLayout, QVBoxLayout, QWidget, 
                           QPushButton, QScrollArea, QFrame, QMainWindow, QLineEdit,
                           QListWidget, QListWidgetItem, QTextEdit, QSplitter,
                           QGraphicsDropShadowEffect, QMessageBox, QComboBox,
                           QCheckBox, QSlider, QSpinBox, QGroupBox, QGridLayout,
                           QTabWidget, QProgressBar)

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from utils import ConfigManager


class ModernCard(QWidget):
    """Modern card widget for sections."""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.init_ui(title)
        
    def init_ui(self, title):
        """Initialize card UI."""
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(45, 45, 50, 0.9),
                    stop:1 rgba(35, 35, 40, 0.9));
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                margin: 5px;
            }
        """)
        
        # Add shadow
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 3)
        self.setGraphicsEffect(shadow)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(10)
        
        if title:
            title_label = QLabel(title)
            title_label.setFont(QFont('Segoe UI', 14, QFont.Bold))
            title_label.setStyleSheet("color: white; margin-bottom: 5px;")
            layout.addWidget(title_label)
            
        self.content_layout = layout
        
    def add_widget(self, widget):
        """Add widget to card content."""
        self.content_layout.addWidget(widget)


class ModernButton(QPushButton):
    """Modern styled button."""
    
    def __init__(self, text, color="#3b82f6", parent=None):
        super().__init__(text, parent)
        self.setStyleSheet(f"""
            QPushButton {{
                background: {color};
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                padding: 12px 24px;
                font-size: 11px;
                min-width: 100px;
            }}
            QPushButton:hover {{
                background: {color}dd;
            }}
            QPushButton:pressed {{
                background: {color}bb;
            }}
        """)


class ModernMainWindow(QMainWindow):
    """Modern main window for Whisper Writer."""
    
    openSettings = pyqtSignal()
    startListening = pyqtSignal()
    closeApp = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """Initialize modern UI."""
        self.setWindowTitle("Whisper Writer")
        self.setGeometry(100, 100, 1000, 700)
        
        # Set dark theme
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(25, 25, 30, 1.0),
                    stop:1 rgba(20, 20, 25, 1.0));
            }
            QTabWidget::pane {
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                background: transparent;
            }
            QTabBar::tab {
                background: rgba(40, 40, 45, 0.8);
                color: rgba(255, 255, 255, 0.7);
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: 500;
            }
            QTabBar::tab:selected {
                background: rgba(59, 130, 246, 0.8);
                color: white;
            }
            QTabBar::tab:hover {
                background: rgba(59, 130, 246, 0.6);
                color: white;
            }
        """)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Header
        self.create_header(main_layout)
        
        # Tab widget for different sections
        self.create_tabs(main_layout)
        
        # Footer with action buttons
        self.create_footer(main_layout)
        
    def create_header(self, layout):
        """Create header section."""
        header_layout = QHBoxLayout()
        
        # Logo and title
        title_layout = QVBoxLayout()
        
        app_title = QLabel("Whisper Writer")
        app_title.setFont(QFont('Segoe UI', 24, QFont.Bold))
        app_title.setStyleSheet("color: white;")
        
        subtitle = QLabel("AI-Powered Voice Transcription")
        subtitle.setFont(QFont('Segoe UI', 12))
        subtitle.setStyleSheet("color: rgba(255, 255, 255, 0.6);")
        
        title_layout.addWidget(app_title)
        title_layout.addWidget(subtitle)
        
        # Status indicator
        status_layout = QVBoxLayout()
        status_layout.setAlignment(Qt.AlignRight)
        
        self.status_indicator = QLabel("● Ready")
        self.status_indicator.setFont(QFont('Segoe UI', 11, QFont.Medium))
        self.status_indicator.setStyleSheet("color: #10b981;")
        self.status_indicator.setAlignment(Qt.AlignRight)
        
        version_label = QLabel("v1.0.0")
        version_label.setFont(QFont('Segoe UI', 9))
        version_label.setStyleSheet("color: rgba(255, 255, 255, 0.4);")
        version_label.setAlignment(Qt.AlignRight)
        
        status_layout.addWidget(self.status_indicator)
        status_layout.addWidget(version_label)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        header_layout.addLayout(status_layout)
        
        layout.addLayout(header_layout)
        
    def create_tabs(self, layout):
        """Create tab widget with different sections."""
        self.tab_widget = QTabWidget()
        
        # Overview tab
        self.create_overview_tab()
        
        # Models tab
        self.create_models_tab()
        
        # Settings tab
        self.create_settings_tab()
        
        # History tab
        self.create_history_tab()
        
        layout.addWidget(self.tab_widget)
        
    def create_overview_tab(self):
        """Create overview tab."""
        overview_widget = QWidget()
        layout = QVBoxLayout(overview_widget)
        layout.setSpacing(20)
        
        # Quick stats cards
        stats_layout = QHBoxLayout()
        
        # Recordings card
        recordings_card = ModernCard("Total Recordings")
        recordings_count = QLabel("0")
        recordings_count.setFont(QFont('Segoe UI', 28, QFont.Bold))
        recordings_count.setStyleSheet("color: #3b82f6;")
        recordings_count.setAlignment(Qt.AlignCenter)
        recordings_card.add_widget(recordings_count)
        
        # Duration card
        duration_card = ModernCard("Total Duration")
        duration_count = QLabel("0 min")
        duration_count.setFont(QFont('Segoe UI', 28, QFont.Bold))
        duration_count.setStyleSheet("color: #10b981;")
        duration_count.setAlignment(Qt.AlignCenter)
        duration_card.add_widget(duration_count)
        
        # Words card
        words_card = ModernCard("Words Transcribed")
        words_count = QLabel("0")
        words_count.setFont(QFont('Segoe UI', 28, QFont.Bold))
        words_count.setStyleSheet("color: #f59e0b;")
        words_count.setAlignment(Qt.AlignCenter)
        words_card.add_widget(words_count)
        
        stats_layout.addWidget(recordings_card)
        stats_layout.addWidget(duration_card)
        stats_layout.addWidget(words_card)
        
        # Quick actions card
        actions_card = ModernCard("Quick Actions")
        
        actions_layout = QGridLayout()
        
        start_btn = ModernButton("Start Listening", "#10b981")
        start_btn.clicked.connect(self.startListening.emit)
        
        history_btn = ModernButton("View History", "#3b82f6")
        history_btn.clicked.connect(self.open_history)
        
        settings_btn = ModernButton("Open Settings", "#6366f1")
        settings_btn.clicked.connect(self.openSettings.emit)
        
        models_btn = ModernButton("Manage Models", "#8b5cf6")
        models_btn.clicked.connect(lambda: self.tab_widget.setCurrentIndex(1))
        
        actions_layout.addWidget(start_btn, 0, 0)
        actions_layout.addWidget(history_btn, 0, 1)
        actions_layout.addWidget(settings_btn, 1, 0)
        actions_layout.addWidget(models_btn, 1, 1)
        
        actions_widget = QWidget()
        actions_widget.setLayout(actions_layout)
        actions_card.add_widget(actions_widget)
        
        layout.addLayout(stats_layout)
        layout.addWidget(actions_card)
        layout.addStretch()
        
        self.tab_widget.addTab(overview_widget, "📊 Overview")
        
    def create_models_tab(self):
        """Create models management tab."""
        models_widget = QWidget()
        layout = QVBoxLayout(models_widget)
        layout.setSpacing(20)
        
        # Current model card
        current_model_card = ModernCard("Current Model")
        
        model_info_layout = QHBoxLayout()
        
        model_name = QLabel("Whisper Base")
        model_name.setFont(QFont('Segoe UI', 16, QFont.Bold))
        model_name.setStyleSheet("color: white;")
        
        model_status = QLabel("● Active")
        model_status.setFont(QFont('Segoe UI', 11))
        model_status.setStyleSheet("color: #10b981;")
        
        model_info_layout.addWidget(model_name)
        model_info_layout.addStretch()
        model_info_layout.addWidget(model_status)
        
        model_details = QLabel("Size: 142MB • Language: Auto-detect • Quality: Good")
        model_details.setFont(QFont('Segoe UI', 10))
        model_details.setStyleSheet("color: rgba(255, 255, 255, 0.7);")
        
        current_model_card.add_widget(QWidget())  # Spacer
        current_model_card.content_layout.addLayout(model_info_layout)
        current_model_card.add_widget(model_details)
        
        # Available models card
        available_models_card = ModernCard("Available Models")
        
        models_list = QListWidget()
        models_list.setStyleSheet("""
            QListWidget {
                background: transparent;
                border: none;
                color: white;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }
            QListWidget::item:hover {
                background: rgba(59, 130, 246, 0.2);
            }
        """)
        
        # Add sample models
        models = [
            "Whisper Tiny (39MB) - Fast, lower quality",
            "Whisper Base (142MB) - Balanced speed/quality",
            "Whisper Small (244MB) - Better quality",
            "Whisper Medium (769MB) - High quality",
            "Whisper Large (1550MB) - Best quality"
        ]
        
        for model in models:
            models_list.addItem(model)
            
        available_models_card.add_widget(models_list)
        
        layout.addWidget(current_model_card)
        layout.addWidget(available_models_card)
        
        self.tab_widget.addTab(models_widget, "🤖 AI Models")
        
    def create_settings_tab(self):
        """Create settings tab."""
        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)
        layout.setSpacing(20)
        
        # Recording settings card
        recording_card = ModernCard("Recording Settings")
        
        recording_layout = QGridLayout()
        
        # Recording mode
        recording_layout.addWidget(QLabel("Recording Mode:"), 0, 0)
        mode_combo = QComboBox()
        mode_combo.addItems(["Hold to Record", "Press to Toggle", "Continuous"])
        mode_combo.setStyleSheet("""
            QComboBox {
                background: rgba(40, 40, 45, 0.8);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 6px;
                color: white;
                padding: 8px;
            }
        """)
        recording_layout.addWidget(mode_combo, 0, 1)
        
        # Audio quality
        recording_layout.addWidget(QLabel("Audio Quality:"), 1, 0)
        quality_combo = QComboBox()
        quality_combo.addItems(["Standard", "High", "Maximum"])
        quality_combo.setStyleSheet(mode_combo.styleSheet())
        recording_layout.addWidget(quality_combo, 1, 1)
        
        recording_widget = QWidget()
        recording_widget.setLayout(recording_layout)
        recording_card.add_widget(recording_widget)
        
        # Hotkeys card
        hotkeys_card = ModernCard("Hotkeys")
        
        hotkeys_layout = QGridLayout()
        
        hotkeys_layout.addWidget(QLabel("Activation Key:"), 0, 0)
        hotkey_edit = QLineEdit("Ctrl+Shift+A")
        hotkey_edit.setStyleSheet("""
            QLineEdit {
                background: rgba(40, 40, 45, 0.8);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 6px;
                color: white;
                padding: 8px;
            }
        """)
        hotkeys_layout.addWidget(hotkey_edit, 0, 1)
        
        hotkeys_widget = QWidget()
        hotkeys_widget.setLayout(hotkeys_layout)
        hotkeys_card.add_widget(hotkeys_widget)
        
        layout.addWidget(recording_card)
        layout.addWidget(hotkeys_card)
        layout.addStretch()
        
        self.tab_widget.addTab(settings_widget, "⚙️ Settings")
        
    def create_history_tab(self):
        """Create history tab."""
        history_widget = QWidget()
        layout = QVBoxLayout(history_widget)
        
        # History will be handled by separate window
        placeholder = QLabel("Click 'View History' to open the full history manager")
        placeholder.setFont(QFont('Segoe UI', 12))
        placeholder.setStyleSheet("color: rgba(255, 255, 255, 0.6);")
        placeholder.setAlignment(Qt.AlignCenter)
        
        view_history_btn = ModernButton("View History", "#3b82f6")
        view_history_btn.clicked.connect(self.open_history)
        
        layout.addStretch()
        layout.addWidget(placeholder)
        layout.addWidget(view_history_btn, alignment=Qt.AlignCenter)
        layout.addStretch()
        
        self.tab_widget.addTab(history_widget, "📝 History")
        
    def create_footer(self, layout):
        """Create footer with action buttons."""
        footer_layout = QHBoxLayout()
        
        # Left side - status
        status_text = QLabel("Ready to transcribe")
        status_text.setFont(QFont('Segoe UI', 10))
        status_text.setStyleSheet("color: rgba(255, 255, 255, 0.6);")
        
        footer_layout.addWidget(status_text)
        footer_layout.addStretch()
        
        # Right side - action buttons
        minimize_btn = ModernButton("Minimize", "#6b7280")
        minimize_btn.clicked.connect(self.hide)
        
        exit_btn = ModernButton("Exit", "#ef4444")
        exit_btn.clicked.connect(self.closeApp.emit)
        
        footer_layout.addWidget(minimize_btn)
        footer_layout.addWidget(exit_btn)
        
        layout.addLayout(footer_layout)
        
    def open_history(self):
        """Open history window."""
        try:
            from .history_window import HistoryWindow
            self.history_window = HistoryWindow([])
            self.history_window.show()
        except Exception as e:
            print(f"Error opening history: {e}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ModernMainWindow()
    window.show()
    sys.exit(app.exec_())
