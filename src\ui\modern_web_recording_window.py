import os
import sys
import j<PERSON>
import random
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QUrl, QObject, pyqtSlot
from PyQt5.QtGui import QFont, QPalette
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QVBoxLayout
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtWebChannel import QWebChannel


class VoiceLevelBridge(QObject):
    """Bridge between Python and JavaScript for real-time voice level updates and button actions."""

    # Signals for button actions
    stop_clicked = pyqtSignal()
    cancel_clicked = pyqtSignal()
    custom_clicked = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.voice_level = 0

    @pyqtSlot(float)
    def update_voice_level(self, level):
        """Update voice level from Python side."""
        self.voice_level = level

    @pyqtSlot(result=float)
    def get_voice_level(self):
        """Get current voice level for JavaScript."""
        return self.voice_level

    @pyqtSlot()
    def stop_recording(self):
        """Called from JavaScript when stop button is clicked."""
        self.stop_clicked.emit()

    @pyqtSlot()
    def cancel_recording(self):
        """Called from JavaScript when cancel button is clicked."""
        self.cancel_clicked.emit()

    @pyqtSlot()
    def custom_action(self):
        """Called from JavaScript when custom button is clicked."""
        self.custom_clicked.emit()


class ModernWebRecordingWindow(QWidget):
    """Modern recording window using HTML/CSS/JavaScript for fluid UI."""
    
    # Signals
    stop_recording = pyqtSignal()
    cancel_recording = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_recording = False
        self.voice_bridge = VoiceLevelBridge()

        # Connect bridge signals to main signals
        self.voice_bridge.stop_clicked.connect(self.stop_recording.emit)
        self.voice_bridge.cancel_clicked.connect(self.cancel_recording.emit)

        self.init_ui()
        self.setup_web_interface()
        
    def init_ui(self):
        """Initialize the modern web-based UI."""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setFixedSize(700, 180)
        
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Web view for modern UI
        self.web_view = QWebEngineView()
        self.web_view.setStyleSheet("background: transparent;")
        layout.addWidget(self.web_view)
        
        # Setup web channel for Python-JavaScript communication
        self.channel = QWebChannel()
        self.channel.registerObject("voiceBridge", self.voice_bridge)
        self.web_view.page().setWebChannel(self.channel)
        
    def setup_web_interface(self):
        """Setup the HTML/CSS/JavaScript interface."""
        html_content = self.create_html_interface()
        self.web_view.setHtml(html_content)
        
    def create_html_interface(self):
        """Create the modern HTML interface exactly like reference image."""
        # Detect system theme
        app = QApplication.instance()
        palette = app.palette()
        is_dark_theme = palette.color(QPalette.Window).lightness() < 128
        
        # Theme colors
        if is_dark_theme:
            bg_color = "rgba(40, 40, 45, 0.85)"
            border_color = "rgba(255, 255, 255, 0.15)"
            text_color = "#ffffff"
            waveform_color = "#60a5fa"
        else:
            bg_color = "rgba(255, 255, 255, 0.85)"
            border_color = "rgba(0, 0, 0, 0.08)"
            text_color = "#374151"
            waveform_color = "#3b82f6"
            
        return f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: transparent;
            overflow: hidden;
            width: 700px;
            height: 180px;
        }}
        
        .recording-container {{
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, {bg_color}, {bg_color});
            border: 1px solid {border_color};
            border-radius: 20px;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            padding: 24px;
            gap: 16px;
        }}
        
        .waveform-container {{
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 16px;
            min-height: 80px;
        }}
        
        .waveform {{
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 2px;
            height: 60px;
        }}
        
        .waveform-bar {{
            width: 3px;
            background: {waveform_color};
            border-radius: 2px;
            transition: height 0.1s ease;
            opacity: 0.7;
        }}
        
        .waveform-bar.active {{
            opacity: 1;
            box-shadow: 0 0 8px {waveform_color};
        }}
        
        .controls {{
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        
        .status {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}
        
        .status-dot {{
            width: 12px;
            height: 12px;
            background: #ef4444;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }}
        
        @keyframes pulse {{
            0% {{ opacity: 1; }}
            50% {{ opacity: 0.5; }}
            100% {{ opacity: 1; }}
        }}
        
        .status-text {{
            color: {text_color};
            font-size: 14px;
            font-weight: 500;
        }}
        
        .buttons {{
            display: flex;
            gap: 12px;
        }}
        
        .btn {{
            padding: 10px 20px;
            border: none;
            border-radius: 10px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }}
        
        .btn-custom {{
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #e9ecef;
        }}
        
        .btn-custom:hover {{
            background: #e9ecef;
        }}
        
        .btn-stop {{
            background: #ef4444;
            color: white;
        }}
        
        .btn-stop:hover {{
            background: #dc2626;
        }}
        
        .btn-cancel {{
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #e9ecef;
        }}
        
        .btn-cancel:hover {{
            background: #e9ecef;
        }}
    </style>
</head>
<body>
    <div class="recording-container">
        <div class="waveform-container">
            <div class="waveform" id="waveform"></div>
        </div>
        
        <div class="controls">
            <div class="status">
                <div class="status-dot"></div>
                <span class="status-text">Recording</span>
            </div>
            
            <div class="buttons">
                <button class="btn btn-custom" onclick="customAction()">Custom</button>
                <button class="btn btn-stop" onclick="stopRecording()">Stop</button>
                <button class="btn btn-cancel" onclick="cancelRecording()">Cancel</button>
            </div>
        </div>
    </div>
    
    <script src="qrc:///qtwebchannel/qwebchannel.js"></script>
    <script>
        let voiceBridge;
        let waveformBars = [];
        let animationId;
        
        // Initialize web channel
        new QWebChannel(qt.webChannelTransport, function(channel) {{
            voiceBridge = channel.objects.voiceBridge;
            initializeWaveform();
            startAnimation();
        }});
        
        function initializeWaveform() {{
            const waveform = document.getElementById('waveform');
            
            // Create 100 waveform bars
            for (let i = 0; i < 100; i++) {{
                const bar = document.createElement('div');
                bar.className = 'waveform-bar';
                bar.style.height = Math.random() * 20 + 5 + 'px';
                waveform.appendChild(bar);
                waveformBars.push(bar);
            }}
        }}
        
        function startAnimation() {{
            function animate() {{
                // Get voice level from Python
                if (voiceBridge) {{
                    const voiceLevel = voiceBridge.get_voice_level();
                    updateWaveform(voiceLevel);
                }}
                
                animationId = requestAnimationFrame(animate);
            }}
            animate();
        }}
        
        function updateWaveform(voiceLevel) {{
            waveformBars.forEach((bar, index) => {{
                // Create realistic waveform based on voice level
                const baseHeight = 5;
                const maxHeight = 50;
                const randomFactor = Math.random() * 0.5 + 0.5;
                const voiceFactor = (voiceLevel / 100) * randomFactor;
                
                const height = baseHeight + (maxHeight - baseHeight) * voiceFactor;
                bar.style.height = height + 'px';
                
                // Add active class for higher bars
                if (height > 25) {{
                    bar.classList.add('active');
                }} else {{
                    bar.classList.remove('active');
                }}
            }});
        }}
        
        function stopRecording() {{
            if (voiceBridge) {{
                voiceBridge.stop_recording();
            }}
        }}

        function cancelRecording() {{
            if (voiceBridge) {{
                voiceBridge.cancel_recording();
            }}
        }}

        function customAction() {{
            if (voiceBridge) {{
                voiceBridge.custom_action();
            }}
        }}
    </script>
</body>
</html>
        """
        
    def update_audio_level(self, level):
        """Update audio level for real-time waveform."""
        # Convert to 0-100 range and add some randomness for realistic effect
        normalized_level = min(100, max(0, level * 2))
        self.voice_bridge.update_voice_level(normalized_level)
        
    def start_recording(self):
        """Start recording animation."""
        self.is_recording = True
        self.show()
        
    def stop_recording_animation(self):
        """Stop recording animation."""
        self.is_recording = False
        
    def position_center_screen(self):
        """Position window in center of screen."""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ModernWebRecordingWindow()
    window.position_center_screen()
    window.start_recording()
    
    # Simulate voice levels for testing
    timer = QTimer()
    def update_level():
        level = random.randint(0, 50)
        window.update_audio_level(level)
    timer.timeout.connect(update_level)
    timer.start(50)
    
    sys.exit(app.exec_())
