import os
import sys
import random
import math
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve, QRect
from PyQt5.QtGui import QFont, QPainter, QBrush, QColor, QPalette, QLinearGradient
from PyQt5.QtWidgets import (QApplication, QLabel, QHBoxLayout, QVBoxLayout, QWidget,
                           QPushButton, QGraphicsDropShadowEffect, QGraphicsOpacityEffect)


class FluidWaveform(QWidget):
    """Ultra-modern fluid waveform visualization with real voice levels."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(650, 80)
        self.bars = []
        self.num_bars = 100
        self.max_height = 60
        self.is_recording = False
        self.audio_level = 0
        
        # Initialize bars with zero height
        for i in range(self.num_bars):
            self.bars.append(0)
        
        # Animation timer for smooth updates
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_waveform)
        self.timer.start(50)  # 20 FPS for smooth animation
        
    def set_recording(self, recording):
        """Set recording state."""
        self.is_recording = recording
        
    def update_audio_level(self, level):
        """Update with real audio level from microphone."""
        self.audio_level = level
        
    def update_waveform(self):
        """Update waveform bars with realistic voice patterns."""
        if not self.is_recording:
            # Decay all bars when not recording
            for i in range(len(self.bars)):
                self.bars[i] = max(0, self.bars[i] - 2)
        else:
            # Create realistic waveform based on actual voice level
            base_level = self.audio_level / 100.0
            
            for i in range(len(self.bars)):
                # Create wave pattern with voice level influence
                wave_factor = math.sin((i * 0.2) + (self.timer.interval() * 0.01)) * 0.3 + 0.7
                noise_factor = random.random() * 0.4 + 0.6
                voice_factor = base_level * wave_factor * noise_factor
                
                target_height = int(voice_factor * self.max_height)
                
                # Smooth transition to target height
                if self.bars[i] < target_height:
                    self.bars[i] = min(target_height, self.bars[i] + 3)
                else:
                    self.bars[i] = max(target_height, self.bars[i] - 2)
                    
        self.update()
        
    def paintEvent(self, event):
        """Paint ultra-modern waveform."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Detect theme
        app = QApplication.instance()
        palette = app.palette()
        is_dark_theme = palette.color(QPalette.Window).lightness() < 128
        
        bar_width = 4
        bar_spacing = 2
        start_x = (self.width() - (self.num_bars * (bar_width + bar_spacing))) // 2
        
        for i, height in enumerate(self.bars):
            x = start_x + i * (bar_width + bar_spacing)
            y = (self.height() - height) // 2
            
            # Create gradient based on height and theme
            gradient = QLinearGradient(0, y, 0, y + height)
            
            if is_dark_theme:
                if height > 40:
                    gradient.setColorAt(0, QColor(96, 165, 250))  # Blue
                    gradient.setColorAt(1, QColor(59, 130, 246))
                elif height > 20:
                    gradient.setColorAt(0, QColor(34, 197, 94))   # Green
                    gradient.setColorAt(1, QColor(22, 163, 74))
                else:
                    gradient.setColorAt(0, QColor(156, 163, 175)) # Gray
                    gradient.setColorAt(1, QColor(107, 114, 128))
            else:
                if height > 40:
                    gradient.setColorAt(0, QColor(59, 130, 246))  # Blue
                    gradient.setColorAt(1, QColor(37, 99, 235))
                elif height > 20:
                    gradient.setColorAt(0, QColor(34, 197, 94))   # Green
                    gradient.setColorAt(1, QColor(22, 163, 74))
                else:
                    gradient.setColorAt(0, QColor(156, 163, 175)) # Gray
                    gradient.setColorAt(1, QColor(107, 114, 128))
            
            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.NoPen)
            painter.drawRoundedRect(x, y, bar_width, height, 2, 2)


class FluidButton(QPushButton):
    """Ultra-modern fluid button with animations."""
    
    def __init__(self, text, button_type="default", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.setFont(QFont('Segoe UI', 14, QFont.Medium))
        self.setCursor(Qt.PointingHandCursor)
        
        # Add hover animation
        self.opacity_effect = QGraphicsOpacityEffect()
        self.setGraphicsEffect(self.opacity_effect)
        
        self.hover_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.hover_animation.setDuration(200)
        self.hover_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        self.apply_style()
        
    def apply_style(self):
        """Apply modern button styling."""
        if self.button_type == "danger":
            self.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ef4444, stop:1 #dc2626);
                    border: none;
                    border-radius: 12px;
                    color: white;
                    font-weight: 600;
                    padding: 12px 24px;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #dc2626, stop:1 #b91c1c);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #b91c1c, stop:1 #991b1b);
                }
            """)
        elif self.button_type == "secondary":
            self.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(248, 250, 252, 0.8), stop:1 rgba(241, 245, 249, 0.8));
                    border: 1px solid rgba(226, 232, 240, 0.8);
                    border-radius: 12px;
                    color: #475569;
                    font-weight: 500;
                    padding: 12px 24px;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(241, 245, 249, 0.9), stop:1 rgba(226, 232, 240, 0.9));
                    border: 1px solid rgba(203, 213, 225, 0.9);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(226, 232, 240, 0.9), stop:1 rgba(203, 213, 225, 0.9));
                }
            """)
        else:  # primary
            self.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #3b82f6, stop:1 #2563eb);
                    border: none;
                    border-radius: 12px;
                    color: white;
                    font-weight: 600;
                    padding: 12px 24px;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #2563eb, stop:1 #1d4ed8);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1d4ed8, stop:1 #1e40af);
                }
            """)
            
    def enterEvent(self, event):
        """Handle mouse enter with animation."""
        self.hover_animation.setStartValue(1.0)
        self.hover_animation.setEndValue(0.8)
        self.hover_animation.start()
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """Handle mouse leave with animation."""
        self.hover_animation.setStartValue(0.8)
        self.hover_animation.setEndValue(1.0)
        self.hover_animation.start()
        super().leaveEvent(event)


class FluidRecordingWindow(QWidget):
    """Ultra-modern fluid recording window exactly like reference image."""
    
    # Signals
    stop_recording = pyqtSignal()
    cancel_recording = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_recording = False
        self.init_ui()
        self.setup_animations()
        
    def init_ui(self):
        """Initialize ultra-modern fluid UI."""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setFixedSize(700, 180)
        
        # Main container with liquid glass effect
        self.main_container = QWidget()
        self.apply_liquid_glass_theme()
        
        # Add premium shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 10)
        self.main_container.setGraphicsEffect(shadow)
        
        # Main layout
        container_layout = QVBoxLayout(self)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.addWidget(self.main_container)
        
        # Content layout
        layout = QVBoxLayout(self.main_container)
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(16)
        
        # Waveform visualization (top)
        self.waveform = FluidWaveform()
        layout.addWidget(self.waveform, 0, Qt.AlignCenter)
        
        # Controls section (bottom)
        self.create_controls(layout)
        
        # Make window draggable
        self.make_draggable()
        
    def apply_liquid_glass_theme(self):
        """Apply liquid glass theme with system theme detection."""
        app = QApplication.instance()
        palette = app.palette()
        is_dark_theme = palette.color(QPalette.Window).lightness() < 128
        
        if is_dark_theme:
            self.main_container.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(40, 40, 45, 0.85),
                        stop:0.5 rgba(35, 35, 40, 0.90),
                        stop:1 rgba(30, 30, 35, 0.85));
                    border: 1px solid rgba(255, 255, 255, 0.15);
                    border-radius: 20px;
                }
            """)
        else:
            self.main_container.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 0.85),
                        stop:0.5 rgba(250, 250, 250, 0.90),
                        stop:1 rgba(245, 245, 245, 0.85));
                    border: 1px solid rgba(0, 0, 0, 0.08);
                    border-radius: 20px;
                }
            """)
            
    def create_controls(self, layout):
        """Create controls exactly like reference image."""
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(20)
        
        # Status section (left)
        status_layout = QHBoxLayout()
        status_layout.setSpacing(10)
        
        # Animated recording dot
        self.status_dot = QLabel("●")
        self.status_dot.setFont(QFont('Segoe UI', 16))
        self.status_dot.setStyleSheet("color: #ef4444;")
        
        # Recording text
        self.status_label = QLabel("Recording")
        self.status_label.setFont(QFont('Segoe UI', 14, QFont.Medium))
        
        # Detect theme for text color
        app = QApplication.instance()
        palette = app.palette()
        is_dark_theme = palette.color(QPalette.Window).lightness() < 128
        text_color = "#ffffff" if is_dark_theme else "#374151"
        self.status_label.setStyleSheet(f"color: {text_color};")
        
        status_layout.addWidget(self.status_dot)
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        # Buttons section (right)
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(12)
        
        # Custom button
        self.custom_btn = FluidButton("Custom", "secondary")
        
        # Stop button
        self.stop_btn = FluidButton("Stop", "danger")
        
        # Cancel button
        self.cancel_btn = FluidButton("Cancel", "secondary")
        
        # Connect signals
        self.stop_btn.clicked.connect(self.stop_recording.emit)
        self.cancel_btn.clicked.connect(self.cancel_recording.emit)
        
        buttons_layout.addWidget(self.custom_btn)
        buttons_layout.addWidget(self.stop_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        # Add to main layout
        controls_layout.addLayout(status_layout)
        controls_layout.addLayout(buttons_layout)
        
        layout.addLayout(controls_layout)
        
    def setup_animations(self):
        """Setup entrance and exit animations."""
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        
    def update_audio_level(self, level):
        """Update audio level for real-time waveform."""
        self.waveform.update_audio_level(level)
        
    def start_recording(self):
        """Start recording with smooth animation."""
        self.is_recording = True
        self.waveform.set_recording(True)
        
        # Animate status dot
        self.animate_status_dot()
        
        # Show with fade in
        self.setWindowOpacity(0)
        self.show()
        self.fade_animation.setStartValue(0)
        self.fade_animation.setEndValue(1)
        self.fade_animation.start()
        
    def animate_status_dot(self):
        """Animate the recording status dot."""
        self.dot_timer = QTimer()
        self.dot_opacity = 1.0
        self.dot_direction = -1
        
        def update_dot():
            self.dot_opacity += self.dot_direction * 0.05
            if self.dot_opacity <= 0.3:
                self.dot_direction = 1
            elif self.dot_opacity >= 1.0:
                self.dot_direction = -1
            
            self.status_dot.setStyleSheet(f"color: rgba(239, 68, 68, {self.dot_opacity});")
            
        self.dot_timer.timeout.connect(update_dot)
        self.dot_timer.start(50)
        
    def stop_recording_animation(self):
        """Stop recording animation."""
        self.is_recording = False
        self.waveform.set_recording(False)
        
        if hasattr(self, 'dot_timer'):
            self.dot_timer.stop()
        
        # Change to green dot for "Done"
        self.status_dot.setStyleSheet("color: #10b981;")
        self.status_label.setText("Done")
        
    def position_center_screen(self):
        """Position window in center of screen."""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)
        
    def make_draggable(self):
        """Make window draggable."""
        self.drag_start_position = None
        
    def mousePressEvent(self, event):
        """Handle mouse press for dragging."""
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.globalPos() - self.frameGeometry().topLeft()
            
    def mouseMoveEvent(self, event):
        """Handle mouse move for dragging."""
        if event.buttons() == Qt.LeftButton and self.drag_start_position:
            self.move(event.globalPos() - self.drag_start_position)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = FluidRecordingWindow()
    window.position_center_screen()
    window.start_recording()
    
    # Simulate voice levels for testing
    timer = QTimer()
    def update_level():
        level = random.randint(0, 50)
        window.update_audio_level(level)
    timer.timeout.connect(update_level)
    timer.start(50)
    
    sys.exit(app.exec_())
