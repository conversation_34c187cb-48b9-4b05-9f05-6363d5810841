import os
import sys
import json
import re
from datetime import datetime
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPainter, QBrush, QColor, QTextCharFormat, QTextCursor
from PyQt5.QtWidgets import (QApplication, QLabel, QHBoxLayout, QVBoxLayout, QWidget, 
                           QPushButton, QMainWindow, QLineEdit, QTextEdit, QScrollArea,
                           QFrame, QSplitter, QGraphicsDropShadowEffect, QCheckBox)


class TimelineEntry(QWidget):
    """Individual timeline entry with timestamp and highlighted text."""
    
    def __init__(self, timestamp, text, search_term="", parent=None):
        super().__init__(parent)
        self.timestamp = timestamp
        self.text = text
        self.search_term = search_term
        self.init_ui()
        
    def init_ui(self):
        """Initialize timeline entry UI."""
        self.setFixedHeight(80)
        self.setStyleSheet("""
            QWidget {
                background: rgba(40, 40, 45, 0.6);
                border: 1px solid rgba(255, 255, 255, 0.05);
                border-radius: 8px;
                margin: 2px 0;
            }
            QWidget:hover {
                background: rgba(45, 45, 50, 0.8);
                border: 1px solid rgba(59, 130, 246, 0.3);
            }
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(12)
        
        # Timestamp
        time_label = QLabel(self.timestamp)
        time_label.setFont(QFont('Segoe UI', 9, QFont.Medium))
        time_label.setStyleSheet("color: #60a5fa; min-width: 80px;")
        time_label.setAlignment(Qt.AlignTop)
        
        # Text content with highlighting
        text_label = QLabel()
        text_label.setFont(QFont('Segoe UI', 10))
        text_label.setStyleSheet("color: white; line-height: 1.4;")
        text_label.setWordWrap(True)
        text_label.setAlignment(Qt.AlignTop)
        
        # Highlight search terms
        display_text = self.text
        if self.search_term and self.search_term.strip():
            # Create highlighted version
            pattern = re.compile(re.escape(self.search_term), re.IGNORECASE)
            display_text = pattern.sub(
                f'<span style="background-color: #3b82f6; color: white; padding: 1px 3px; border-radius: 2px;">{self.search_term}</span>',
                display_text
            )
        
        text_label.setText(display_text)
        
        layout.addWidget(time_label)
        layout.addWidget(text_label, 1)


class ModernSearchBox(QLineEdit):
    """Modern search box with styling."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setPlaceholderText("Search recordings...")
        self.setFixedHeight(40)
        self.setStyleSheet("""
            QLineEdit {
                background: rgba(40, 40, 45, 0.8);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 20px;
                color: white;
                padding: 0 16px;
                font-size: 12px;
                font-family: 'Segoe UI';
            }
            QLineEdit:focus {
                border: 1px solid #3b82f6;
                background: rgba(45, 45, 50, 0.9);
            }
        """)


class ModernHistoryWindow(QMainWindow):
    """Modern history window matching the reference design."""
    
    def __init__(self, recording_history=None, parent=None):
        super().__init__(parent)
        self.recording_history = recording_history or []
        self.current_search = ""
        self.filtered_recordings = self.recording_history.copy()
        self.init_ui()
        self.load_recordings()
        
    def init_ui(self):
        """Initialize modern history UI like reference."""
        self.setWindowTitle("Whisper Writer - History")
        self.setGeometry(100, 100, 1200, 800)
        
        # Dark theme styling
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(25, 25, 30, 1.0),
                    stop:1 rgba(20, 20, 25, 1.0));
            }
        """)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Header section
        self.create_header(layout)
        
        # Content area with splitter
        self.create_content_area(layout)
        
    def create_header(self, layout):
        """Create header with title and controls."""
        header_layout = QHBoxLayout()
        
        # Title section
        title_layout = QVBoxLayout()
        
        # Main title
        title = QLabel("History")
        title.setFont(QFont('Segoe UI', 24, QFont.Bold))
        title.setStyleSheet("color: white;")
        
        # Subtitle with count
        subtitle = QLabel(f"{len(self.recording_history)} recordings")
        subtitle.setFont(QFont('Segoe UI', 12))
        subtitle.setStyleSheet("color: rgba(255, 255, 255, 0.6);")
        
        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)
        
        # Search and controls
        controls_layout = QVBoxLayout()
        
        # Search box
        self.search_box = ModernSearchBox()
        self.search_box.textChanged.connect(self.filter_recordings)
        
        # Filter options
        filter_layout = QHBoxLayout()
        
        self.show_matches_only = QCheckBox("Show Matches Only")
        self.show_matches_only.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 11px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 3px;
                background: rgba(40, 40, 45, 0.8);
            }
            QCheckBox::indicator:checked {
                background: #3b82f6;
                border: 1px solid #3b82f6;
            }
        """)
        self.show_matches_only.toggled.connect(self.filter_recordings)
        
        filter_layout.addWidget(self.show_matches_only)
        filter_layout.addStretch()
        
        # Export button
        export_btn = QPushButton("Export")
        export_btn.setFixedSize(80, 32)
        export_btn.setStyleSheet("""
            QPushButton {
                background: rgba(59, 130, 246, 0.8);
                border: none;
                border-radius: 16px;
                color: white;
                font-weight: 500;
                font-size: 11px;
            }
            QPushButton:hover {
                background: rgba(59, 130, 246, 1.0);
            }
        """)
        
        filter_layout.addWidget(export_btn)
        
        controls_layout.addWidget(self.search_box)
        controls_layout.addLayout(filter_layout)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        header_layout.addLayout(controls_layout)
        
        layout.addLayout(header_layout)
        
    def create_content_area(self, layout):
        """Create main content area with timeline and detail view."""
        # Splitter for timeline and detail
        splitter = QSplitter(Qt.Horizontal)
        
        # Timeline area (left)
        timeline_widget = self.create_timeline_area()
        
        # Detail area (right)
        detail_widget = self.create_detail_area()
        
        splitter.addWidget(timeline_widget)
        splitter.addWidget(detail_widget)
        splitter.setSizes([400, 800])  # Timeline smaller, detail larger
        
        layout.addWidget(splitter)
        
    def create_timeline_area(self):
        """Create timeline area with recordings list."""
        timeline_widget = QWidget()
        timeline_layout = QVBoxLayout(timeline_widget)
        timeline_layout.setContentsMargins(0, 0, 0, 0)
        timeline_layout.setSpacing(0)
        
        # Timeline header
        timeline_header = QLabel("Timeline")
        timeline_header.setFont(QFont('Segoe UI', 14, QFont.Bold))
        timeline_header.setStyleSheet("color: white; padding: 10px 0;")
        timeline_layout.addWidget(timeline_header)
        
        # Scrollable timeline
        self.timeline_scroll = QScrollArea()
        self.timeline_scroll.setWidgetResizable(True)
        self.timeline_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarNever)
        self.timeline_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 4px;
            }
        """)
        
        # Timeline content widget
        self.timeline_content = QWidget()
        self.timeline_layout = QVBoxLayout(self.timeline_content)
        self.timeline_layout.setContentsMargins(0, 0, 0, 0)
        self.timeline_layout.setSpacing(4)
        
        self.timeline_scroll.setWidget(self.timeline_content)
        timeline_layout.addWidget(self.timeline_scroll)
        
        return timeline_widget
        
    def create_detail_area(self):
        """Create detail area for selected recording."""
        detail_widget = QWidget()
        detail_layout = QVBoxLayout(detail_widget)
        detail_layout.setContentsMargins(20, 0, 0, 0)
        detail_layout.setSpacing(15)
        
        # Detail header
        detail_header = QLabel("Transcript")
        detail_header.setFont(QFont('Segoe UI', 14, QFont.Bold))
        detail_header.setStyleSheet("color: white; padding: 10px 0;")
        detail_layout.addWidget(detail_header)
        
        # Transcript text area
        self.transcript_text = QTextEdit()
        self.transcript_text.setStyleSheet("""
            QTextEdit {
                background: rgba(40, 40, 45, 0.6);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                color: white;
                padding: 16px;
                font-family: 'Segoe UI';
                font-size: 12px;
                line-height: 1.5;
            }
        """)
        self.transcript_text.setPlaceholderText("Select a recording to view transcript")
        detail_layout.addWidget(self.transcript_text)
        
        # Action buttons
        actions_layout = QHBoxLayout()
        
        copy_btn = QPushButton("Copy")
        copy_btn.setStyleSheet(self.get_button_style("#10b981"))
        
        download_btn = QPushButton("Download")
        download_btn.setStyleSheet(self.get_button_style("#3b82f6"))
        
        actions_layout.addWidget(copy_btn)
        actions_layout.addWidget(download_btn)
        actions_layout.addStretch()
        
        detail_layout.addLayout(actions_layout)
        
        return detail_widget
        
    def get_button_style(self, color):
        """Get consistent button styling."""
        return f"""
            QPushButton {{
                background: {color};
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                padding: 8px 16px;
                font-size: 11px;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background: {color}dd;
            }}
            QPushButton:pressed {{
                background: {color}bb;
            }}
        """
        
    def load_recordings(self):
        """Load recordings into timeline."""
        self.update_timeline()
        
    def filter_recordings(self):
        """Filter recordings based on search term."""
        search_term = self.search_box.text().strip()
        self.current_search = search_term
        
        if search_term:
            self.filtered_recordings = [
                recording for recording in self.recording_history
                if search_term.lower() in recording.get('transcription', '').lower()
            ]
        else:
            self.filtered_recordings = self.recording_history.copy()
            
        self.update_timeline()
        
    def update_timeline(self):
        """Update timeline display."""
        # Clear existing entries
        for i in reversed(range(self.timeline_layout.count())):
            child = self.timeline_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
                
        # Add filtered recordings
        recordings_to_show = self.filtered_recordings
        if self.show_matches_only.isChecked() and self.current_search:
            recordings_to_show = [r for r in recordings_to_show if self.current_search.lower() in r.get('transcription', '').lower()]
            
        for recording in recordings_to_show:
            timestamp = recording.get('timestamp', 'Unknown')
            text = recording.get('transcription', '')
            
            # Format timestamp for display
            try:
                dt = datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S')
                display_time = dt.strftime('%H:%M:%S')
            except:
                display_time = timestamp
                
            entry = TimelineEntry(display_time, text, self.current_search)
            entry.mousePressEvent = lambda event, r=recording: self.select_recording(r)
            self.timeline_layout.addWidget(entry)
            
        # Add stretch at end
        self.timeline_layout.addStretch()
        
    def select_recording(self, recording):
        """Select and display recording details."""
        text = recording.get('transcription', '')
        
        # Highlight search terms in detail view
        if self.current_search and self.current_search.strip():
            # Use HTML for highlighting
            pattern = re.compile(re.escape(self.current_search), re.IGNORECASE)
            highlighted_text = pattern.sub(
                f'<span style="background-color: #3b82f6; color: white;">{self.current_search}</span>',
                text
            )
            self.transcript_text.setHtml(highlighted_text)
        else:
            self.transcript_text.setPlainText(text)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    # Sample data for testing
    sample_recordings = [
        {
            'timestamp': '2024-01-15 10:30:15',
            'transcription': 'You know that feeling, catching the perfect wave, when everything lines up just right, the timing, motion, rhythm, it\'s effortless.'
        },
        {
            'timestamp': '2024-01-15 10:30:22',
            'transcription': 'That\'s what wave refuefully for me.'
        },
        {
            'timestamp': '2024-01-15 10:30:31',
            'transcription': 'I used to spend hours, days, even digging through interviews and audio recordings, searching for that one perfect moment.'
        }
    ]
    
    window = ModernHistoryWindow(sample_recordings)
    window.show()
    sys.exit(app.exec_())
