import sys
import os
import json
from datetime import datetime
from PyQt5.QtCore import (Qt, pyqtSignal, QTimer, QPropertyAnimation, 
                         QEasingCurve, QPoint)
from PyQt5.QtGui import (QFont, QPainter, QBrush, QColor, QPen, QCursor)
from PyQt5.QtWidgets import (QApplication, QLabel, QHBoxLayout, QVBoxLayout, QWidget, 
                           QPushButton, QGraphicsDropShadowEffect)

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from utils import ConfigManager


class SimpleVoiceMeter(QWidget):
    """Simple horizontal voice meter that always works."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(120, 20)
        self.levels = [0] * 10
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_animation)
        self.timer.start(50)
        
    def set_level(self, level):
        """Set audio level - Green=Low, Red=High."""
        target_bars = int((level / 100.0) * len(self.levels))
        
        for i in range(len(self.levels)):
            if i < target_bars:
                self.levels[i] = min(100, level)
            else:
                self.levels[i] = max(0, self.levels[i] - 20)
                
    def update_animation(self):
        for i in range(len(self.levels)):
            if self.levels[i] > 0:
                self.levels[i] = max(0, self.levels[i] - 5)
        self.update()
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        bar_width = 3
        bar_spacing = 2
        start_x = 5
        
        for i, level in enumerate(self.levels):
            if level > 3:
                x = start_x + i * (bar_width + bar_spacing)
                bar_height = int((level / 100.0) * (self.height() - 4))
                y = (self.height() - bar_height) // 2
                
                # CORRECT: Green for low (quiet), Red for high (loud)
                if i < len(self.levels) * 0.4:  # Low levels = Green
                    color = QColor(34, 197, 94, 200)
                elif i < len(self.levels) * 0.7:  # Medium = Yellow
                    color = QColor(251, 191, 36, 200)
                else:  # High levels = Red
                    color = QColor(239, 68, 68, 200)
                    
                painter.setBrush(QBrush(color))
                painter.setPen(Qt.NoPen)
                painter.drawRoundedRect(x, y, bar_width, bar_height, 1, 1)


class ReliableStatusWindow(QWidget):
    """Reliable status window that always shows up."""
    
    statusSignal = pyqtSignal(str)
    closeSignal = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.is_recording = False
        self.recording_history = []
        self.history_file = os.path.join('src', 'recording_history.json')
        
        self.init_ui()
        self.load_history()
        self.position_window()
        
    def init_ui(self):
        """Initialize reliable UI that definitely shows."""
        # Use normal window flags for reliability
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        self.setFixedSize(500, 50)
        
        # Dark styling without transparency issues
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgb(40, 40, 45),
                    stop:1 rgb(30, 30, 35));
                border: 2px solid rgb(59, 130, 246);
                border-radius: 25px;
            }
        """)
        
        # Add shadow for visibility
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 150))
        shadow.setOffset(0, 3)
        self.setGraphicsEffect(shadow)
        
        # Main layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(15)
        
        # Status section
        self.create_status_section(layout)
        
        # Voice meter
        self.voice_meter = SimpleVoiceMeter()
        layout.addWidget(self.voice_meter)
        
        # Action buttons
        self.create_action_buttons(layout)
        
    def create_status_section(self, layout):
        """Create status section."""
        status_layout = QHBoxLayout()
        
        # Status dot
        self.status_dot = QLabel("●")
        self.status_dot.setFont(QFont('Segoe UI', 10))
        self.status_dot.setStyleSheet("color: #10b981;")  # Green
        
        # Status text
        self.status_label = QLabel('Ready')
        self.status_label.setFont(QFont('Segoe UI', 10, QFont.Medium))
        self.status_label.setStyleSheet("color: white;")
        
        status_layout.addWidget(self.status_dot)
        status_layout.addWidget(self.status_label)
        layout.addLayout(status_layout)
        
    def create_action_buttons(self, layout):
        """Create action buttons."""
        layout.addStretch()
        
        # History button
        history_btn = QPushButton("History")
        history_btn.setStyleSheet(self.get_button_style())
        history_btn.clicked.connect(self.open_history_window)
        layout.addWidget(history_btn)
        
        # Settings button  
        settings_btn = QPushButton("Settings")
        settings_btn.setStyleSheet(self.get_button_style())
        settings_btn.clicked.connect(self.open_settings)
        layout.addWidget(settings_btn)
        
        # Minimize button
        minimize_btn = QPushButton("−")
        minimize_btn.setFixedSize(25, 25)
        minimize_btn.setStyleSheet("""
            QPushButton {
                background: rgba(59, 130, 246, 0.8);
                border: none;
                border-radius: 12px;
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: rgba(59, 130, 246, 1.0);
            }
        """)
        minimize_btn.clicked.connect(self.hide)
        layout.addWidget(minimize_btn)
        
    def get_button_style(self):
        """Get consistent button styling."""
        return """
            QPushButton {
                background: rgba(59, 130, 246, 0.8);
                border: none;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                padding: 6px 12px;
                font-size: 10px;
            }
            QPushButton:hover {
                background: rgba(59, 130, 246, 1.0);
            }
            QPushButton:pressed {
                background: rgba(37, 99, 235, 0.9);
            }
        """
        
    def position_window(self):
        """Position window at top-right of screen."""
        screen = QApplication.primaryScreen().geometry()
        
        # Position at top-right
        x = screen.width() - self.width() - 20
        y = 20
        
        self.move(x, y)
        
    def update_status(self, status, is_recording=False):
        """Update status display and ALWAYS show window."""
        self.is_recording = is_recording
        self.status_label.setText(status)
        
        if is_recording:
            self.status_dot.setStyleSheet("color: #ef4444;")  # Red
        else:
            self.status_dot.setStyleSheet("color: #10b981;")  # Green
            
        # ALWAYS show window when status updates
        if not self.isVisible():
            self.show()
            self.raise_()
            self.activateWindow()
            
    def update_voice_level(self, level):
        """Update voice level meter."""
        self.voice_meter.set_level(level)
        
        # Ensure window is visible during recording
        if not self.isVisible():
            self.show()
            self.raise_()
        
    def open_history_window(self):
        """Open the history management window."""
        try:
            from history_window import HistoryWindow
            self.history_window = HistoryWindow(self.recording_history)
            self.history_window.show()
        except Exception as e:
            print(f"Error opening history window: {e}")
        
    def open_settings(self):
        """Open settings window."""
        try:
            from settings_window import SettingsWindow
            self.settings_window = SettingsWindow()
            self.settings_window.show()
        except Exception as e:
            print(f"Error opening settings window: {e}")
        
    def load_history(self):
        """Load recording history."""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.recording_history = json.load(f)
        except Exception as e:
            print(f"Error loading history: {e}")
            self.recording_history = []
            
    def save_history(self):
        """Save recording history."""
        try:
            os.makedirs(os.path.dirname(self.history_file), exist_ok=True)
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.recording_history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving history: {e}")
            
    def add_recording(self, text, duration, timestamp=None):
        """Add new recording to history."""
        if timestamp is None:
            timestamp = datetime.now().isoformat()
            
        recording = {
            'text': text,
            'duration': duration,
            'timestamp': timestamp,
            'id': len(self.recording_history)
        }
        
        self.recording_history.insert(0, recording)  # Add to beginning
        self.save_history()
        
    def mousePressEvent(self, event):
        """Handle mouse press for dragging."""
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.globalPos() - self.frameGeometry().topLeft()
            
    def mouseMoveEvent(self, event):
        """Handle mouse move for dragging."""
        if event.buttons() == Qt.LeftButton and hasattr(self, 'drag_start_position'):
            self.move(event.globalPos() - self.drag_start_position)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ReliableStatusWindow()
    window.show()
    
    # Test the voice meter
    import random
    timer = QTimer()
    def update_level():
        level = random.randint(0, 100)
        window.update_voice_level(level)
        window.update_status(f"Recording... {level}%", True)
    
    timer.timeout.connect(update_level)
    timer.start(100)
    
    sys.exit(app.exec_())
