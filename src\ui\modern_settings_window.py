import os
import sys
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QColor
from PyQt5.QtWidgets import (QApplication, QLabel, QHBoxLayout, QVBoxLayout, QWidget,
                           QPushButton, QMainWindow, QComboBox, QCheckBox, QLineEdit,
                           QScrollArea, QFrame, QSlider, QSpinBox, QGraphicsDropShadowEffect)

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from utils import ConfigManager


class SettingsCard(QWidget):
    """Modern settings card with glass effect."""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.init_ui(title)
        
    def init_ui(self, title):
        """Initialize settings card UI."""
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(45, 45, 50, 0.9),
                    stop:1 rgba(35, 35, 40, 0.9));
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 16px;
                margin: 8px;
            }
        """)
        
        # Add shadow
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(15)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 3)
        self.setGraphicsEffect(shadow)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(16)
        
        if title:
            title_label = QLabel(title)
            title_label.setFont(QFont('Segoe UI', 16, QFont.Bold))
            title_label.setStyleSheet("color: white; margin-bottom: 8px;")
            layout.addWidget(title_label)
            
        self.content_layout = layout


class ModernToggle(QCheckBox):
    """Modern toggle switch."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet("""
            QCheckBox {
                spacing: 8px;
                color: white;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 44px;
                height: 24px;
                border-radius: 12px;
                background: rgba(75, 85, 99, 0.8);
                border: 1px solid rgba(156, 163, 175, 0.3);
            }
            QCheckBox::indicator:checked {
                background: #3b82f6;
                border: 1px solid #3b82f6;
            }
            QCheckBox::indicator:checked::before {
                content: '';
                width: 18px;
                height: 18px;
                border-radius: 9px;
                background: white;
                position: absolute;
                right: 3px;
                top: 3px;
            }
            QCheckBox::indicator::before {
                content: '';
                width: 18px;
                height: 18px;
                border-radius: 9px;
                background: white;
                position: absolute;
                left: 3px;
                top: 3px;
            }
        """)


class ModernComboBox(QComboBox):
    """Modern styled combo box."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedHeight(40)
        self.setStyleSheet("""
            QComboBox {
                background: rgba(40, 40, 45, 0.8);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                color: white;
                padding: 0 12px;
                font-size: 12px;
                min-width: 200px;
            }
            QComboBox:focus {
                border: 1px solid #3b82f6;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
                width: 12px;
                height: 12px;
            }
            QComboBox QAbstractItemView {
                background: rgba(40, 40, 45, 0.95);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                color: white;
                selection-background-color: #3b82f6;
            }
        """)


class ModernSlider(QSlider):
    """Modern styled slider."""
    
    def __init__(self, orientation=Qt.Horizontal, parent=None):
        super().__init__(orientation, parent)
        self.setStyleSheet("""
            QSlider::groove:horizontal {
                border: none;
                height: 6px;
                background: rgba(75, 85, 99, 0.8);
                border-radius: 3px;
            }
            QSlider::handle:horizontal {
                background: #3b82f6;
                border: none;
                width: 20px;
                height: 20px;
                border-radius: 10px;
                margin: -7px 0;
            }
            QSlider::handle:horizontal:hover {
                background: #60a5fa;
            }
            QSlider::sub-page:horizontal {
                background: #3b82f6;
                border-radius: 3px;
            }
        """)


class SettingRow(QWidget):
    """Individual setting row with label and control."""
    
    def __init__(self, label_text, control_widget, description="", parent=None):
        super().__init__(parent)
        self.init_ui(label_text, control_widget, description)
        
    def init_ui(self, label_text, control_widget, description):
        """Initialize setting row UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 8, 0, 8)
        layout.setSpacing(8)
        
        # Main row with label and control
        main_row = QHBoxLayout()
        
        # Label
        label = QLabel(label_text)
        label.setFont(QFont('Segoe UI', 12, QFont.Medium))
        label.setStyleSheet("color: white;")
        
        main_row.addWidget(label)
        main_row.addStretch()
        main_row.addWidget(control_widget)
        
        layout.addLayout(main_row)
        
        # Description if provided
        if description:
            desc_label = QLabel(description)
            desc_label.setFont(QFont('Segoe UI', 10))
            desc_label.setStyleSheet("color: rgba(255, 255, 255, 0.6);")
            desc_label.setWordWrap(True)
            layout.addWidget(desc_label)
            
        # Separator line
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("border: 1px solid rgba(255, 255, 255, 0.1);")
        layout.addWidget(separator)


class ModernSettingsWindow(QMainWindow):
    """Modern settings window with glass theme."""
    
    settings_saved = pyqtSignal()
    settings_closed = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.schema = ConfigManager.get_schema()
        self.init_ui()
        
    def init_ui(self):
        """Initialize modern settings UI."""
        self.setWindowTitle("Settings")
        self.setGeometry(100, 100, 800, 700)
        
        # Dark theme styling
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(25, 25, 30, 1.0),
                    stop:1 rgba(20, 20, 25, 1.0));
            }
        """)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Header
        self.create_header(layout)
        
        # Settings content
        self.create_settings_content(layout)
        
        # Action buttons
        self.create_action_buttons(layout)
        
    def create_header(self, layout):
        """Create header section."""
        header_layout = QHBoxLayout()
        
        # Title
        title = QLabel("Settings")
        title.setFont(QFont('Segoe UI', 28, QFont.Bold))
        title.setStyleSheet("color: white;")
        
        # Subtitle
        subtitle = QLabel("Configure your Whisper Writer preferences")
        subtitle.setFont(QFont('Segoe UI', 12))
        subtitle.setStyleSheet("color: rgba(255, 255, 255, 0.7);")
        
        title_layout = QVBoxLayout()
        title_layout.addWidget(title)
        title_layout.addWidget(subtitle)
        
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
    def create_settings_content(self, layout):
        """Create scrollable settings content."""
        # Scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarNever)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 4px;
            }
        """)
        
        # Content widget
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(20)
        
        # Recording settings
        self.create_recording_settings(content_layout)
        
        # Model settings
        self.create_model_settings(content_layout)
        
        # UI settings
        self.create_ui_settings(content_layout)
        
        # Advanced settings
        self.create_advanced_settings(content_layout)
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        
    def create_recording_settings(self, layout):
        """Create recording settings card."""
        card = SettingsCard("Recording")
        
        # Auto-start recording
        auto_start_toggle = ModernToggle()
        auto_start_toggle.setChecked(True)
        card.content_layout.addWidget(
            SettingRow("Auto-start recording", auto_start_toggle, 
                      "Automatically start listening when the app launches")
        )
        
        # Recording mode
        mode_combo = ModernComboBox()
        mode_combo.addItems(["Push to Talk", "Continuous", "Voice Activation"])
        card.content_layout.addWidget(
            SettingRow("Recording mode", mode_combo, 
                      "Choose how recording is triggered")
        )
        
        # Audio device
        device_combo = ModernComboBox()
        device_combo.addItems(["Default", "Microphone (Built-in)", "USB Headset"])
        card.content_layout.addWidget(
            SettingRow("Audio device", device_combo, 
                      "Select the microphone to use for recording")
        )
        
        layout.addWidget(card)
        
    def create_model_settings(self, layout):
        """Create model settings card."""
        card = SettingsCard("Transcription Model")
        
        # Use API toggle
        use_api_toggle = ModernToggle()
        card.content_layout.addWidget(
            SettingRow("Use OpenAI API", use_api_toggle, 
                      "Use OpenAI's Whisper API instead of local model")
        )
        
        # Model size
        model_combo = ModernComboBox()
        model_combo.addItems(["tiny", "base", "small", "medium", "large"])
        model_combo.setCurrentText("base")
        card.content_layout.addWidget(
            SettingRow("Local model size", model_combo, 
                      "Larger models are more accurate but slower")
        )
        
        layout.addWidget(card)
        
    def create_ui_settings(self, layout):
        """Create UI settings card."""
        card = SettingsCard("User Interface")
        
        # Theme selection
        theme_combo = ModernComboBox()
        theme_combo.addItems(["Glass Dark", "Glass Light", "Solid Dark", "Solid Light"])
        card.content_layout.addWidget(
            SettingRow("Theme", theme_combo, 
                      "Choose the visual theme for the application")
        )
        
        # Window opacity
        opacity_slider = ModernSlider()
        opacity_slider.setRange(50, 100)
        opacity_slider.setValue(85)
        card.content_layout.addWidget(
            SettingRow("Window opacity", opacity_slider, 
                      "Adjust transparency of application windows")
        )
        
        # Always on top
        always_top_toggle = ModernToggle()
        always_top_toggle.setChecked(True)
        card.content_layout.addWidget(
            SettingRow("Always on top", always_top_toggle, 
                      "Keep application windows above other applications")
        )
        
        layout.addWidget(card)
        
    def create_advanced_settings(self, layout):
        """Create advanced settings card."""
        card = SettingsCard("Advanced")
        
        # Copy to clipboard
        copy_toggle = ModernToggle()
        card.content_layout.addWidget(
            SettingRow("Copy to clipboard", copy_toggle, 
                      "Automatically copy transcriptions to clipboard")
        )
        
        # Paste instead of type
        paste_toggle = ModernToggle()
        card.content_layout.addWidget(
            SettingRow("Paste instead of typing", paste_toggle, 
                      "Use paste operation instead of simulating keystrokes")
        )
        
        layout.addWidget(card)
        
    def create_action_buttons(self, layout):
        """Create action buttons."""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(12)
        
        # Reset button
        reset_btn = QPushButton("Reset to Defaults")
        reset_btn.setFixedHeight(40)
        reset_btn.setStyleSheet("""
            QPushButton {
                background: rgba(75, 85, 99, 0.8);
                border: 1px solid rgba(156, 163, 175, 0.3);
                border-radius: 8px;
                color: white;
                font-weight: 500;
                padding: 0 20px;
                font-size: 12px;
            }
            QPushButton:hover {
                background: rgba(75, 85, 99, 1.0);
            }
        """)
        
        # Save button
        save_btn = QPushButton("Save Settings")
        save_btn.setFixedHeight(40)
        save_btn.setStyleSheet("""
            QPushButton {
                background: #3b82f6;
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                padding: 0 20px;
                font-size: 12px;
            }
            QPushButton:hover {
                background: #60a5fa;
            }
        """)
        
        save_btn.clicked.connect(self.save_settings)
        
        button_layout.addStretch()
        button_layout.addWidget(reset_btn)
        button_layout.addWidget(save_btn)
        
        layout.addLayout(button_layout)
        
    def save_settings(self):
        """Save settings and emit signal."""
        # TODO: Implement actual settings saving
        self.settings_saved.emit()
        self.close()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ModernSettingsWindow()
    window.show()
    sys.exit(app.exec_())
