import os
import sys
import random
import math
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt5.QtGui import QFont, QPainter, QBrush, QColor, QPen, QPalette
from PyQt5.QtWidgets import (QA<PERSON>lication, QLabel, QHBoxLayout, QVBoxLayout, QWidget,
                           QPushButton, QGraphicsDropShadowEffect)


class WaveformVisualization(QWidget):
    """Animated waveform visualization like the reference image."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(650, 80)  # Much larger like reference
        self.bars = []
        self.num_bars = 150  # More bars for better detail
        self.max_height = 60  # Taller bars
        self.is_recording = False
        self.audio_level = 0
        
        # Initialize bars with random heights for demo
        for i in range(self.num_bars):
            self.bars.append(random.randint(8, 40))  # Taller bars
        
        # Animation timer
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_waveform)
        self.timer.start(50)  # Update every 50ms
        
    def set_recording(self, recording):
        """Set recording state."""
        self.is_recording = recording
        if not recording:
            # Fade out animation when stopping
            for i in range(len(self.bars)):
                self.bars[i] = max(2, self.bars[i] - 2)
        
    def set_audio_level(self, level):
        """Set current audio level (0-100)."""
        self.audio_level = level
        
    def update_waveform(self):
        """Update waveform animation."""
        if self.is_recording:
            # Animate bars based on audio level
            for i in range(len(self.bars)):
                if random.random() < 0.3:  # 30% chance to update each bar
                    base_height = max(2, int(self.audio_level * 0.4))
                    variation = random.randint(-10, 15)
                    new_height = max(2, min(self.max_height, base_height + variation))
                    self.bars[i] = new_height
        else:
            # Decay animation when not recording
            for i in range(len(self.bars)):
                if self.bars[i] > 2:
                    self.bars[i] = max(2, self.bars[i] - 1)
                    
        self.update()
        
    def paintEvent(self, event):
        """Paint the waveform like reference images."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        bar_width = 2  # Wider bars
        bar_spacing = 1
        start_x = (self.width() - (self.num_bars * (bar_width + bar_spacing))) // 2

        for i, height in enumerate(self.bars):
            x = start_x + i * (bar_width + bar_spacing)
            y = (self.height() - height) // 2

            # Dark bars like in reference images
            if self.is_recording:
                # Dark gray/black bars when recording
                alpha = min(255, 150 + height * 2)
                color = QColor(60, 60, 60, alpha)
            else:
                # Lighter when not recording
                alpha = min(150, 80 + height)
                color = QColor(120, 120, 120, alpha)

            painter.setBrush(QBrush(color))
            painter.setPen(Qt.NoPen)
            painter.drawRoundedRect(x, y, bar_width, height, 0, 0)


class ModernButton(QPushButton):
    """Modern styled button for recording controls."""
    
    def __init__(self, text, button_type="default", parent=None):
        super().__init__(text, parent)
        self.button_type = button_type
        self.setFixedHeight(32)
        self.setMinimumWidth(80)
        self.setFont(QFont('Segoe UI', 10, QFont.Medium))
        self.apply_style()
        
    def apply_style(self):
        """Apply button styling based on type to match reference images."""
        if self.button_type == "primary":
            # Blue primary button
            self.setStyleSheet("""
                QPushButton {
                    background: #3b82f6;
                    border: none;
                    border-radius: 6px;
                    color: white;
                    font-weight: 500;
                    padding: 0 12px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background: #2563eb;
                }
                QPushButton:pressed {
                    background: #1d4ed8;
                }
            """)
        elif self.button_type == "danger":
            # Red stop button like reference
            self.setStyleSheet("""
                QPushButton {
                    background: #ef4444;
                    border: none;
                    border-radius: 10px;
                    color: white;
                    font-weight: 600;
                    padding: 0 20px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: #dc2626;
                }
                QPushButton:pressed {
                    background: #b91c1c;
                }
            """)
        elif self.button_type == "secondary":
            # Secondary button like Custom and Cancel in reference
            self.setStyleSheet("""
                QPushButton {
                    background: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 10px;
                    color: #495057;
                    font-weight: 500;
                    padding: 0 20px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: #e9ecef;
                    border: 1px solid #dee2e6;
                }
                QPushButton:pressed {
                    background: #dee2e6;
                }
            """)
        else:
            # Default/primary button
            self.setStyleSheet("""
                QPushButton {
                    background: #3b82f6;
                    border: none;
                    border-radius: 10px;
                    color: white;
                    font-weight: 600;
                    padding: 0 20px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background: #2563eb;
                }
                QPushButton:pressed {
                    background: #1d4ed8;
                }
            """)


class WaveformRecordingWindow(QWidget):
    """Modern recording window with waveform visualization like the reference."""
    
    # Signals
    stop_recording = pyqtSignal()
    cancel_recording = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_recording = False
        self.init_ui()
        self.setup_animations()
        
    def init_ui(self):
        """Initialize the modern recording UI exactly like reference image."""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setFixedSize(700, 180)  # Much larger to match reference exactly

        # Main container with liquid glass background and system theme support
        self.main_container = QWidget()
        self.apply_liquid_glass_theme()

        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setColor(QColor(0, 0, 0, 60))
        shadow.setOffset(0, 8)
        self.main_container.setGraphicsEffect(shadow)

        # Main layout
        container_layout = QVBoxLayout(self)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.addWidget(self.main_container)

        # Content layout - exactly like reference image
        layout = QVBoxLayout(self.main_container)
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(16)

        # Waveform visualization (top) - much larger like reference
        self.waveform = WaveformVisualization()
        self.waveform.setFixedSize(650, 80)  # Much larger like reference
        layout.addWidget(self.waveform, 0, Qt.AlignCenter)

        # Controls section (bottom) - horizontal layout like reference
        self.create_reference_controls(layout)

        # Make window draggable
        self.make_draggable()

    def apply_liquid_glass_theme(self):
        """Apply liquid glass theme with system theme detection."""
        # Detect system theme
        app = QApplication.instance()
        palette = app.palette()
        is_dark_theme = palette.color(QPalette.Window).lightness() < 128

        if is_dark_theme:
            # Dark liquid glass theme
            self.main_container.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(40, 40, 45, 0.85),
                        stop:0.5 rgba(35, 35, 40, 0.90),
                        stop:1 rgba(30, 30, 35, 0.85));
                    border: 1px solid rgba(255, 255, 255, 0.15);
                    border-radius: 20px;
                }
            """)
        else:
            # Light liquid glass theme
            self.main_container.setStyleSheet("""
                QWidget {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(255, 255, 255, 0.85),
                        stop:0.5 rgba(250, 250, 250, 0.90),
                        stop:1 rgba(245, 245, 245, 0.85));
                    border: 1px solid rgba(0, 0, 0, 0.08);
                    border-radius: 20px;
                }
            """)

    def create_reference_controls(self, layout):
        """Create controls exactly like reference image."""
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(20)

        # Recording status with icon (left)
        status_layout = QHBoxLayout()
        status_layout.setSpacing(8)

        # Red recording dot
        self.status_dot = QLabel("●")
        self.status_dot.setFont(QFont('Segoe UI', 16))
        self.status_dot.setStyleSheet("color: #ef4444;")

        # Recording text
        self.status_label = QLabel("Recording")
        self.status_label.setFont(QFont('Segoe UI', 14, QFont.Medium))
        self.status_label.setStyleSheet("color: #374151;")

        status_layout.addWidget(self.status_dot)
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()

        # Control buttons (right) - like reference
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(12)

        # Custom button
        self.custom_btn = ModernButton("Custom", "secondary")
        self.custom_btn.setFixedSize(80, 40)

        # Stop button
        self.stop_btn = ModernButton("Stop", "danger")
        self.stop_btn.setFixedSize(80, 40)

        # Cancel button
        self.cancel_btn = ModernButton("Cancel", "secondary")
        self.cancel_btn.setFixedSize(80, 40)

        # Connect signals
        self.stop_btn.clicked.connect(lambda: self.stop_recording.emit())
        self.cancel_btn.clicked.connect(lambda: self.cancel_recording.emit())

        buttons_layout.addWidget(self.custom_btn)
        buttons_layout.addWidget(self.stop_btn)
        buttons_layout.addWidget(self.cancel_btn)

        # Add to main layout
        controls_layout.addLayout(status_layout)
        controls_layout.addLayout(buttons_layout)

        layout.addLayout(controls_layout)

    def create_status_section_vertical(self, layout):
        """Create status section for vertical layout like reference images."""
        status_layout = QHBoxLayout()
        status_layout.setSpacing(10)

        # Red recording dot - larger
        self.status_dot = QLabel("●")
        self.status_dot.setFont(QFont('Segoe UI', 16))  # Larger
        self.status_dot.setStyleSheet("color: #ef4444;")  # Red

        # Recording text - larger
        self.status_label = QLabel("Recording")
        self.status_label.setFont(QFont('Segoe UI', 14, QFont.Medium))  # Larger
        self.status_label.setStyleSheet("color: #374151;")  # Dark gray

        status_layout.addWidget(self.status_dot)
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()  # Push to left

        layout.addLayout(status_layout)

    def create_status_section(self, layout):
        """Create status section like reference images."""
        status_layout = QHBoxLayout()
        status_layout.setSpacing(8)

        # Red recording dot
        self.status_dot = QLabel("●")
        self.status_dot.setFont(QFont('Segoe UI', 12))
        self.status_dot.setStyleSheet("color: #ef4444;")  # Red

        # Recording text
        self.status_label = QLabel("Recording")
        self.status_label.setFont(QFont('Segoe UI', 12, QFont.Medium))
        self.status_label.setStyleSheet("color: #374151;")  # Dark gray

        status_layout.addWidget(self.status_dot)
        status_layout.addWidget(self.status_label)

        layout.addLayout(status_layout)

    def create_controls_horizontal(self, layout):
        """Create horizontal controls like reference images."""
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(8)

        # Control buttons with keyboard shortcuts - larger
        self.stop_btn = ModernButton("Stop", "danger")
        self.stop_btn.setText("Stop")
        self.stop_btn.setFixedSize(80, 36)  # Larger buttons

        # Space key indicator
        space_label = QLabel("⌃Space")
        space_label.setFont(QFont('Segoe UI', 12))  # Larger font
        space_label.setStyleSheet("color: #6b7280;")

        self.cancel_btn = ModernButton("Cancel", "default")
        self.cancel_btn.setText("Cancel")
        self.cancel_btn.setFixedSize(80, 36)  # Larger buttons

        # Esc key indicator
        esc_label = QLabel("Esc")
        esc_label.setFont(QFont('Segoe UI', 12))  # Larger font
        esc_label.setStyleSheet("color: #6b7280;")

        # Connect signals
        self.stop_btn.clicked.connect(lambda: self.stop_recording.emit())
        self.cancel_btn.clicked.connect(lambda: self.cancel_recording.emit())

        controls_layout.addWidget(self.stop_btn)
        controls_layout.addWidget(space_label)
        controls_layout.addWidget(self.cancel_btn)
        controls_layout.addWidget(esc_label)

        layout.addLayout(controls_layout)

    def create_done_controls(self, layout):
        """Create controls for done state like second reference image."""
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(8)

        # Record again button
        self.record_btn = ModernButton("Record", "primary")
        self.record_btn.setFixedSize(60, 28)

        # Space key indicator
        space_label = QLabel("⌃Space")
        space_label.setFont(QFont('Segoe UI', 10))
        space_label.setStyleSheet("color: #6b7280;")

        # Close button
        self.close_btn = ModernButton("Close", "default")
        self.close_btn.setFixedSize(60, 28)

        # Esc key indicator
        esc_label = QLabel("Esc")
        esc_label.setFont(QFont('Segoe UI', 10))
        esc_label.setStyleSheet("color: #6b7280;")

        controls_layout.addWidget(self.record_btn)
        controls_layout.addWidget(space_label)
        controls_layout.addWidget(self.close_btn)
        controls_layout.addWidget(esc_label)

        layout.addLayout(controls_layout)

    def create_controls(self, layout):
        """Create recording controls like the reference."""
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(12)
        
        # Recording status indicator
        self.status_indicator = QLabel("● Recording")
        self.status_indicator.setFont(QFont('Segoe UI', 10, QFont.Medium))
        self.status_indicator.setStyleSheet("color: #ef4444;")  # Red
        
        controls_layout.addWidget(self.status_indicator)
        controls_layout.addStretch()
        
        # Control buttons
        self.default_btn = ModernButton("Default", "default")
        self.stop_btn = ModernButton("Stop", "danger")
        self.cancel_btn = ModernButton("Cancel", "default")
        
        # Add keyboard shortcuts display
        self.default_btn.setText("Default ⌃⌥K")
        self.stop_btn.setText("Stop ⌃Space")
        self.cancel_btn.setText("Cancel Esc")
        
        # Connect signals
        self.stop_btn.clicked.connect(lambda: self.stop_recording.emit())
        self.cancel_btn.clicked.connect(lambda: self.cancel_recording.emit())
        
        controls_layout.addWidget(self.default_btn)
        controls_layout.addWidget(self.stop_btn)
        controls_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(controls_layout)
        
    def setup_animations(self):
        """Setup window animations."""
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        
    def start_recording(self):
        """Start recording animation."""
        self.is_recording = True
        self.waveform.set_recording(True)
        self.status_dot.setStyleSheet("color: #ef4444;")  # Red
        self.status_label.setText("Recording")

        # Show window with fade in
        self.setWindowOpacity(0)
        self.show()
        self.fade_animation.setStartValue(0)
        self.fade_animation.setEndValue(1)
        self.fade_animation.start()

    def stop_recording_animation(self):
        """Stop recording animation."""
        self.is_recording = False
        self.waveform.set_recording(False)
        self.status_dot.setStyleSheet("color: #10b981;")  # Green
        self.status_label.setText("Done")
        
    def update_audio_level(self, level):
        """Update audio level for waveform."""
        self.waveform.set_audio_level(level)
        
    def make_draggable(self):
        """Make window draggable."""
        self.drag_start_position = None
        
    def mousePressEvent(self, event):
        """Handle mouse press for dragging."""
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.globalPos() - self.frameGeometry().topLeft()
            
    def mouseMoveEvent(self, event):
        """Handle mouse move for dragging."""
        if event.buttons() == Qt.LeftButton and self.drag_start_position:
            new_pos = event.globalPos() - self.drag_start_position
            self.move(new_pos)
            
    def position_center_screen(self):
        """Position window at center of screen."""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = WaveformRecordingWindow()
    window.position_center_screen()
    window.start_recording()
    sys.exit(app.exec_())
