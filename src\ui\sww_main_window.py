import sys
import os
import json
from datetime import datetime
from PyQt5.QtCore import (Qt, pyqtSignal, QTimer, QSize, QSettings)
from PyQt5.QtGui import (QFont, QIcon, QPainter, QBrush, QColor, 
                        QLinearGradient, QFontMetrics, QPixmap)
from PyQt5.QtWidgets import (QApplication, QLabel, QHBoxLayout, QVBoxLayout, QWidget, 
                           QPushButton, QScrollArea, QFrame, QMainWindow, QLineEdit,
                           QListWidget, QListWidgetItem, QTextEdit, QSplitter,
                           QGraphicsDropShadowEffect, QMessageBox, QComboBox,
                           QCheckBox, QSlider, QSpinBox, QGroupBox, QGridLayout,
                           QTabWidget, QProgressBar, QStackedWidget)

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from utils import ConfigManager


class GlassCard(QWidget):
    """Glass morphism card widget."""
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.init_ui(title)
        
    def init_ui(self, title):
        """Initialize glass card UI."""
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:1 rgba(255, 255, 255, 0.05));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 16px;
                backdrop-filter: blur(20px);
            }
        """)
        
        # Add glass effect shadow
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setColor(QColor(0, 0, 0, 40))
        shadow.setOffset(0, 8)
        self.setGraphicsEffect(shadow)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 20, 24, 20)
        layout.setSpacing(16)
        
        if title:
            title_label = QLabel(title)
            title_label.setFont(QFont('Segoe UI', 16, QFont.Bold))
            title_label.setStyleSheet("color: white; margin-bottom: 8px;")
            layout.addWidget(title_label)
            
        self.content_layout = layout


class IconButton(QPushButton):
    """Modern icon-based button with glass effect."""
    
    def __init__(self, icon_text, tooltip="", color="#3b82f6", parent=None):
        super().__init__(parent)
        self.setText(icon_text)
        self.setToolTip(tooltip)
        self.setFixedSize(48, 48)
        self.setFont(QFont('Segoe UI', 16))
        
        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:1 rgba(255, 255, 255, 0.08));
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 24px;
                color: white;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.25),
                    stop:1 rgba(255, 255, 255, 0.15));
                border: 1px solid rgba(255, 255, 255, 0.4);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.1),
                    stop:1 rgba(255, 255, 255, 0.05));
            }}
        """)


class StatusIndicator(QWidget):
    """Status indicator with single color like reference."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(24, 24)
        self.is_recording = False
        
    def set_recording(self, recording):
        """Set recording status."""
        self.is_recording = recording
        self.update()
        
    def paintEvent(self, event):
        """Paint status indicator."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Draw circle
        if self.is_recording:
            color = QColor(239, 68, 68)  # Red for recording
        else:
            color = QColor(34, 197, 94)  # Green for ready
            
        painter.setBrush(QBrush(color))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(4, 4, 16, 16)


class VoiceLevelBar(QWidget):
    """Single color voice level bar like reference image."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(200, 8)
        self.level = 0
        
    def set_level(self, level):
        """Set voice level (0-100)."""
        self.level = level
        self.update()
        
    def paintEvent(self, event):
        """Paint voice level bar."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Background
        painter.setBrush(QBrush(QColor(255, 255, 255, 30)))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(0, 0, self.width(), self.height(), 4, 4)
        
        # Level bar - single color like reference
        if self.level > 0:
            level_width = int((self.level / 100.0) * self.width())
            painter.setBrush(QBrush(QColor(59, 130, 246)))  # Blue like reference
            painter.drawRoundedRect(0, 0, level_width, self.height(), 4, 4)


class SWWMainWindow(QMainWindow):
    """Super Whisper Writer - Main integrated window."""
    
    openSettings = pyqtSignal()
    startListening = pyqtSignal()
    closeApp = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.settings = QSettings('SWW', 'SuperWhisperWriter')
        self.recording_history = []
        self.current_recording = None
        
        self.init_ui()
        self.load_position()
        self.load_history()
        
    def init_ui(self):
        """Initialize glass morphism UI."""
        self.setWindowTitle("SWW - Super Whisper Writer")
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setFixedSize(900, 600)
        
        # Main container with glass effect
        self.main_container = QWidget()
        self.main_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(20, 20, 25, 0.85),
                    stop:1 rgba(30, 30, 35, 0.85));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 24px;
                backdrop-filter: blur(40px);
            }
        """)
        
        # Add glass shadow
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(40)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 12)
        self.main_container.setGraphicsEffect(shadow)
        
        # Set main layout
        container_layout = QVBoxLayout(self)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.addWidget(self.main_container)
        
        # Main content layout
        main_layout = QVBoxLayout(self.main_container)
        main_layout.setContentsMargins(24, 24, 24, 24)
        main_layout.setSpacing(20)
        
        # Header
        self.create_header(main_layout)
        
        # Status bar (like reference image)
        self.create_status_bar(main_layout)
        
        # Main content area
        self.create_content_area(main_layout)
        
        # Make window draggable
        self.make_draggable()
        
    def create_header(self, layout):
        """Create header with branding."""
        header_layout = QHBoxLayout()
        
        # SWW Logo and title
        logo_layout = QVBoxLayout()
        
        app_title = QLabel("SWW")
        app_title.setFont(QFont('Segoe UI', 28, QFont.Bold))
        app_title.setStyleSheet("color: white;")
        
        subtitle = QLabel("Super Whisper Writer")
        subtitle.setFont(QFont('Segoe UI', 11))
        subtitle.setStyleSheet("color: rgba(255, 255, 255, 0.7);")
        
        logo_layout.addWidget(app_title)
        logo_layout.addWidget(subtitle)
        
        # Action buttons (icons only)
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(12)
        
        # Settings icon
        settings_btn = IconButton("⚙️", "Settings")
        settings_btn.clicked.connect(lambda: self.show_page("settings"))
        
        # History icon
        history_btn = IconButton("📝", "History")
        history_btn.clicked.connect(lambda: self.show_page("history"))
        
        # Models icon
        models_btn = IconButton("🤖", "AI Models")
        models_btn.clicked.connect(lambda: self.show_page("models"))
        
        # Minimize icon
        minimize_btn = IconButton("−", "Minimize")
        minimize_btn.clicked.connect(self.hide)
        
        # Close icon
        close_btn = IconButton("✕", "Close")
        close_btn.clicked.connect(self.closeApp.emit)
        
        actions_layout.addWidget(settings_btn)
        actions_layout.addWidget(history_btn)
        actions_layout.addWidget(models_btn)
        actions_layout.addStretch()
        actions_layout.addWidget(minimize_btn)
        actions_layout.addWidget(close_btn)
        
        header_layout.addLayout(logo_layout)
        header_layout.addStretch()
        header_layout.addLayout(actions_layout)
        
        layout.addLayout(header_layout)
        
    def create_status_bar(self, layout):
        """Create status bar like reference image."""
        status_card = GlassCard()
        status_layout = QHBoxLayout()
        status_layout.setContentsMargins(20, 12, 20, 12)
        
        # Status indicator
        self.status_indicator = StatusIndicator()
        
        # Status text
        self.status_label = QLabel("Ready")
        self.status_label.setFont(QFont('Segoe UI', 12, QFont.Medium))
        self.status_label.setStyleSheet("color: white;")
        
        # Voice level bar
        self.voice_level_bar = VoiceLevelBar()
        
        # Recording controls (icons only)
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(8)
        
        # Copy option checkbox
        self.copy_checkbox = QCheckBox("Copy")
        self.copy_checkbox.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 11px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                background: rgba(255, 255, 255, 0.1);
            }
            QCheckBox::indicator:checked {
                background: #3b82f6;
                border: 1px solid #3b82f6;
            }
        """)
        
        # Paste option checkbox
        self.paste_checkbox = QCheckBox("Paste")
        self.paste_checkbox.setStyleSheet(self.copy_checkbox.styleSheet())
        
        controls_layout.addWidget(self.copy_checkbox)
        controls_layout.addWidget(self.paste_checkbox)
        
        status_layout.addWidget(self.status_indicator)
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.voice_level_bar)
        status_layout.addStretch()
        status_layout.addLayout(controls_layout)
        
        status_widget = QWidget()
        status_widget.setLayout(status_layout)
        status_card.content_layout.addWidget(status_widget)
        
        layout.addWidget(status_card)
        
    def create_content_area(self, layout):
        """Create main content area with stacked pages."""
        self.content_stack = QStackedWidget()
        
        # Overview page
        self.create_overview_page()
        
        # History page (integrated)
        self.create_history_page()
        
        # Settings page (integrated)
        self.create_settings_page()
        
        # Models page (integrated)
        self.create_models_page()
        
        layout.addWidget(self.content_stack)
        
    def create_overview_page(self):
        """Create overview page."""
        overview_widget = QWidget()
        layout = QVBoxLayout(overview_widget)
        layout.setSpacing(20)
        
        # Quick stats
        stats_layout = QHBoxLayout()
        
        # Stats cards with glass effect
        recordings_card = GlassCard("Recordings")
        recordings_count = QLabel("0")
        recordings_count.setFont(QFont('Segoe UI', 32, QFont.Bold))
        recordings_count.setStyleSheet("color: #3b82f6;")
        recordings_count.setAlignment(Qt.AlignCenter)
        recordings_card.content_layout.addWidget(recordings_count)
        
        duration_card = GlassCard("Duration")
        duration_count = QLabel("0m")
        duration_count.setFont(QFont('Segoe UI', 32, QFont.Bold))
        duration_count.setStyleSheet("color: #10b981;")
        duration_count.setAlignment(Qt.AlignCenter)
        duration_card.content_layout.addWidget(duration_count)
        
        words_card = GlassCard("Words")
        words_count = QLabel("0")
        words_count.setFont(QFont('Segoe UI', 32, QFont.Bold))
        words_count.setStyleSheet("color: #f59e0b;")
        words_count.setAlignment(Qt.AlignCenter)
        words_card.content_layout.addWidget(words_count)
        
        stats_layout.addWidget(recordings_card)
        stats_layout.addWidget(duration_card)
        stats_layout.addWidget(words_card)
        
        # Recent activity
        recent_card = GlassCard("Recent Activity")
        recent_label = QLabel("No recent recordings")
        recent_label.setStyleSheet("color: rgba(255, 255, 255, 0.6); font-size: 12px;")
        recent_label.setAlignment(Qt.AlignCenter)
        recent_card.content_layout.addWidget(recent_label)
        
        layout.addLayout(stats_layout)
        layout.addWidget(recent_card)
        layout.addStretch()
        
        self.content_stack.addWidget(overview_widget)
        
    def create_history_page(self):
        """Create integrated history page."""
        history_widget = QWidget()
        layout = QHBoxLayout(history_widget)
        layout.setSpacing(20)
        
        # History list (left side)
        history_card = GlassCard("Recording History")
        
        self.history_list = QListWidget()
        self.history_list.setStyleSheet("""
            QListWidget {
                background: transparent;
                border: none;
                color: white;
                font-size: 11px;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                margin: 2px;
            }
            QListWidget::item:hover {
                background: rgba(255, 255, 255, 0.1);
            }
            QListWidget::item:selected {
                background: rgba(59, 130, 246, 0.3);
            }
        """)
        
        history_card.content_layout.addWidget(self.history_list)
        
        # Detail panel (right side)
        detail_card = GlassCard("Details")
        
        self.detail_text = QTextEdit()
        self.detail_text.setStyleSheet("""
            QTextEdit {
                background: rgba(255, 255, 255, 0.05);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                color: white;
                padding: 12px;
                font-size: 11px;
            }
        """)
        self.detail_text.setPlaceholderText("Select a recording to view details")
        
        detail_card.content_layout.addWidget(self.detail_text)
        
        layout.addWidget(history_card, 1)
        layout.addWidget(detail_card, 1)
        
        self.content_stack.addWidget(history_widget)

    def create_settings_page(self):
        """Create integrated settings page."""
        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)
        layout.setSpacing(20)

        # Recording settings
        recording_card = GlassCard("Recording Options")
        recording_layout = QGridLayout()

        # Copy/Paste options
        recording_layout.addWidget(QLabel("Copy to clipboard:"), 0, 0)
        copy_combo = QComboBox()
        copy_combo.addItems(["Disabled", "After recording", "Real-time"])
        copy_combo.setStyleSheet(self.get_combo_style())
        recording_layout.addWidget(copy_combo, 0, 1)

        recording_layout.addWidget(QLabel("Auto-paste:"), 1, 0)
        paste_combo = QComboBox()
        paste_combo.addItems(["Disabled", "Type word-by-word", "Paste all at once"])
        paste_combo.setStyleSheet(self.get_combo_style())
        recording_layout.addWidget(paste_combo, 1, 1)

        recording_layout.addWidget(QLabel("Recording mode:"), 2, 0)
        mode_combo = QComboBox()
        mode_combo.addItems(["Hold to record", "Press to toggle", "Continuous"])
        mode_combo.setStyleSheet(self.get_combo_style())
        recording_layout.addWidget(mode_combo, 2, 1)

        recording_widget = QWidget()
        recording_widget.setLayout(recording_layout)
        recording_card.content_layout.addWidget(recording_widget)

        # UI Customization
        ui_card = GlassCard("UI Customization")
        ui_layout = QGridLayout()

        ui_layout.addWidget(QLabel("Theme:"), 0, 0)
        theme_combo = QComboBox()
        theme_combo.addItems(["Glass Dark", "Glass Light", "Solid Dark", "Solid Light"])
        theme_combo.setStyleSheet(self.get_combo_style())
        ui_layout.addWidget(theme_combo, 0, 1)

        ui_layout.addWidget(QLabel("Window opacity:"), 1, 0)
        opacity_slider = QSlider(Qt.Horizontal)
        opacity_slider.setRange(50, 100)
        opacity_slider.setValue(85)
        opacity_slider.setStyleSheet(self.get_slider_style())
        ui_layout.addWidget(opacity_slider, 1, 1)

        ui_layout.addWidget(QLabel("Always on top:"), 2, 0)
        always_top_check = QCheckBox()
        always_top_check.setChecked(True)
        always_top_check.setStyleSheet(self.get_checkbox_style())
        ui_layout.addWidget(always_top_check, 2, 1)

        ui_widget = QWidget()
        ui_widget.setLayout(ui_layout)
        ui_card.content_layout.addWidget(ui_widget)

        # Hotkeys
        hotkeys_card = GlassCard("Hotkeys")
        hotkeys_layout = QGridLayout()

        hotkeys_layout.addWidget(QLabel("Record hotkey:"), 0, 0)
        hotkey_edit = QLineEdit("Ctrl+Shift+A")
        hotkey_edit.setStyleSheet(self.get_input_style())
        hotkeys_layout.addWidget(hotkey_edit, 0, 1)

        hotkeys_widget = QWidget()
        hotkeys_widget.setLayout(hotkeys_layout)
        hotkeys_card.content_layout.addWidget(hotkeys_widget)

        layout.addWidget(recording_card)
        layout.addWidget(ui_card)
        layout.addWidget(hotkeys_card)
        layout.addStretch()

        self.content_stack.addWidget(settings_widget)

    def create_models_page(self):
        """Create integrated models page."""
        models_widget = QWidget()
        layout = QVBoxLayout(models_widget)
        layout.setSpacing(20)

        # Current model
        current_card = GlassCard("Current Model")
        current_layout = QHBoxLayout()

        model_info = QVBoxLayout()
        model_name = QLabel("Whisper Base")
        model_name.setFont(QFont('Segoe UI', 16, QFont.Bold))
        model_name.setStyleSheet("color: white;")

        model_details = QLabel("Size: 142MB • Quality: Good • Speed: Fast")
        model_details.setStyleSheet("color: rgba(255, 255, 255, 0.7); font-size: 11px;")

        model_info.addWidget(model_name)
        model_info.addWidget(model_details)

        status_label = QLabel("● Active")
        status_label.setStyleSheet("color: #10b981; font-weight: bold;")

        current_layout.addLayout(model_info)
        current_layout.addStretch()
        current_layout.addWidget(status_label)

        current_widget = QWidget()
        current_widget.setLayout(current_layout)
        current_card.content_layout.addWidget(current_widget)

        # Available models
        available_card = GlassCard("Available Models")

        models_list = QListWidget()
        models_list.setStyleSheet("""
            QListWidget {
                background: transparent;
                border: none;
                color: white;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                margin: 2px;
            }
            QListWidget::item:hover {
                background: rgba(255, 255, 255, 0.1);
            }
        """)

        models = [
            "Whisper Tiny - 39MB (Fastest)",
            "Whisper Base - 142MB (Balanced) ✓",
            "Whisper Small - 244MB (Better)",
            "Whisper Medium - 769MB (High Quality)",
            "Whisper Large - 1550MB (Best Quality)"
        ]

        for model in models:
            models_list.addItem(model)

        available_card.content_layout.addWidget(models_list)

        layout.addWidget(current_card)
        layout.addWidget(available_card)

        self.content_stack.addWidget(models_widget)

    def get_combo_style(self):
        """Get combobox styling."""
        return """
            QComboBox {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                color: white;
                padding: 8px 12px;
                font-size: 11px;
            }
            QComboBox:hover {
                background: rgba(255, 255, 255, 0.15);
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
        """

    def get_input_style(self):
        """Get input styling."""
        return """
            QLineEdit {
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                color: white;
                padding: 8px 12px;
                font-size: 11px;
            }
            QLineEdit:focus {
                border: 1px solid #3b82f6;
                background: rgba(255, 255, 255, 0.15);
            }
        """

    def get_slider_style(self):
        """Get slider styling."""
        return """
            QSlider::groove:horizontal {
                border: none;
                height: 6px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 3px;
            }
            QSlider::handle:horizontal {
                background: #3b82f6;
                border: none;
                width: 16px;
                height: 16px;
                border-radius: 8px;
                margin: -5px 0;
            }
            QSlider::sub-page:horizontal {
                background: #3b82f6;
                border-radius: 3px;
            }
        """

    def get_checkbox_style(self):
        """Get checkbox styling."""
        return """
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                background: rgba(255, 255, 255, 0.1);
            }
            QCheckBox::indicator:checked {
                background: #3b82f6;
                border: 1px solid #3b82f6;
            }
        """

    def show_page(self, page_name):
        """Show specific page."""
        pages = {
            "overview": 0,
            "history": 1,
            "settings": 2,
            "models": 3
        }
        if page_name in pages:
            self.content_stack.setCurrentIndex(pages[page_name])

    def make_draggable(self):
        """Make window draggable."""
        self.drag_start_position = None

    def mousePressEvent(self, event):
        """Handle mouse press for dragging."""
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.globalPos() - self.frameGeometry().topLeft()

    def mouseMoveEvent(self, event):
        """Handle mouse move for dragging."""
        if event.buttons() == Qt.LeftButton and self.drag_start_position:
            new_pos = event.globalPos() - self.drag_start_position
            self.move(new_pos)

    def mouseReleaseEvent(self, event):
        """Handle mouse release - save position."""
        if event.button() == Qt.LeftButton:
            self.save_position()

    def save_position(self):
        """Save window position."""
        self.settings.setValue("window_pos", self.pos())

    def load_position(self):
        """Load window position or center if first time."""
        pos = self.settings.value("window_pos")
        if pos:
            self.move(pos)
        else:
            # Center on screen for first time
            screen = QApplication.primaryScreen().geometry()
            x = (screen.width() - self.width()) // 2
            y = (screen.height() - self.height()) // 2
            self.move(x, y)

    def load_history(self):
        """Load recording history."""
        try:
            history_file = os.path.join('src', 'recording_history.json')
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    self.recording_history = json.load(f)
                    self.update_history_display()
        except Exception as e:
            print(f"Error loading history: {e}")

    def update_history_display(self):
        """Update history list display."""
        self.history_list.clear()
        for recording in self.recording_history[:10]:  # Show last 10
            timestamp = datetime.fromisoformat(recording['timestamp'])
            item_text = f"{timestamp.strftime('%m/%d %H:%M')} - {recording['text'][:50]}..."
            self.history_list.addItem(item_text)

    def update_status(self, status, is_recording=False):
        """Update status display."""
        self.status_label.setText(status)
        self.status_indicator.set_recording(is_recording)

        # Show window when recording starts
        if is_recording and not self.isVisible():
            self.show()
            self.raise_()

    def update_voice_level(self, level):
        """Update voice level bar."""
        self.voice_level_bar.set_level(level)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = SWWMainWindow()
    window.show()
    sys.exit(app.exec_())
