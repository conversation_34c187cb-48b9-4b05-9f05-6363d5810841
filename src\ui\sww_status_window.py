import sys
import os
import json
from datetime import datetime
from PyQt5.QtCore import (Qt, pyqtSignal, QTimer, QSettings)
from PyQt5.QtGui import (QFont, QPainter, QBrush, QColor, QLinearGradient)
from PyQt5.QtWidgets import (QApplication, QLabel, QHBoxLayout, QVBoxLayout, QWidget, 
                           QPushButton, QGraphicsDropShadowEffect, QCheckBox)

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from utils import ConfigManager


class SWWVoiceLevel(QWidget):
    """Voice level display exactly like reference image - single color bar."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(150, 6)
        self.level = 0
        
    def set_level(self, level):
        """Set voice level (0-100)."""
        self.level = level
        self.update()
        
    def paintEvent(self, event):
        """Paint voice level exactly like reference."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # Background track
        painter.setBrush(QBrush(QColor(255, 255, 255, 40)))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(0, 0, self.width(), self.height(), 3, 3)
        
        # Level indicator - single blue color like reference
        if self.level > 0:
            level_width = int((self.level / 100.0) * self.width())
            painter.setBrush(QBrush(QColor(59, 130, 246)))  # Blue like reference
            painter.drawRoundedRect(0, 0, level_width, self.height(), 3, 3)


class SWWStatusWindow(QWidget):
    """SWW Status window exactly like reference image."""
    
    statusSignal = pyqtSignal(str)
    closeSignal = pyqtSignal()
    openMainWindow = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.settings = QSettings('SWW', 'SuperWhisperWriter')
        self.is_recording = False
        self.copy_enabled = False
        self.paste_enabled = False
        
        self.init_ui()
        self.load_position()
        
    def init_ui(self):
        """Initialize UI exactly like reference image."""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setFixedSize(500, 50)
        
        # Main container with glass effect like reference
        self.main_container = QWidget()
        self.main_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(40, 40, 45, 0.95),
                    stop:1 rgba(30, 30, 35, 0.95));
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 25px;
            }
        """)
        
        # Add shadow like reference
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setColor(QColor(0, 0, 0, 100))
        shadow.setOffset(0, 5)
        self.main_container.setGraphicsEffect(shadow)
        
        # Main layout
        container_layout = QVBoxLayout(self)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.addWidget(self.main_container)
        
        # Content layout
        layout = QHBoxLayout(self.main_container)
        layout.setContentsMargins(20, 12, 20, 12)
        layout.setSpacing(15)
        
        # Status section (left)
        self.create_status_section(layout)
        
        # Voice level (center)
        self.voice_level = SWWVoiceLevel()
        layout.addWidget(self.voice_level)
        
        # Controls (right)
        self.create_controls_section(layout)
        
        # Make draggable
        self.make_draggable()
        
    def create_status_section(self, layout):
        """Create status section like reference."""
        status_layout = QHBoxLayout()
        status_layout.setSpacing(8)
        
        # Status dot
        self.status_dot = QLabel("●")
        self.status_dot.setFont(QFont('Segoe UI', 10))
        self.status_dot.setStyleSheet("color: #10b981;")  # Green
        
        # Status text
        self.status_label = QLabel('Ready')
        self.status_label.setFont(QFont('Segoe UI', 11, QFont.Medium))
        self.status_label.setStyleSheet("color: white;")
        
        status_layout.addWidget(self.status_dot)
        status_layout.addWidget(self.status_label)
        
        layout.addLayout(status_layout)
        
    def create_controls_section(self, layout):
        """Create controls section like reference."""
        layout.addStretch()
        
        # Copy/Paste options
        options_layout = QHBoxLayout()
        options_layout.setSpacing(12)
        
        # Copy checkbox
        self.copy_check = QCheckBox("Copy")
        self.copy_check.setStyleSheet("""
            QCheckBox {
                color: white;
                font-size: 10px;
                font-weight: 500;
            }
            QCheckBox::indicator {
                width: 14px;
                height: 14px;
                border-radius: 3px;
                border: 1px solid rgba(255, 255, 255, 0.4);
                background: rgba(255, 255, 255, 0.1);
            }
            QCheckBox::indicator:checked {
                background: #3b82f6;
                border: 1px solid #3b82f6;
            }
        """)
        self.copy_check.toggled.connect(self.on_copy_toggled)
        
        # Paste checkbox
        self.paste_check = QCheckBox("Paste")
        self.paste_check.setStyleSheet(self.copy_check.styleSheet())
        self.paste_check.toggled.connect(self.on_paste_toggled)
        
        options_layout.addWidget(self.copy_check)
        options_layout.addWidget(self.paste_check)
        
        # Main window button (icon)
        main_btn = QPushButton("⚙")
        main_btn.setFixedSize(28, 28)
        main_btn.setToolTip("Open SWW")
        main_btn.setStyleSheet("""
            QPushButton {
                background: rgba(59, 130, 246, 0.8);
                border: none;
                border-radius: 14px;
                color: white;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background: rgba(59, 130, 246, 1.0);
            }
        """)
        main_btn.clicked.connect(self.openMainWindow.emit)
        
        # Close button (icon)
        close_btn = QPushButton("✕")
        close_btn.setFixedSize(28, 28)
        close_btn.setToolTip("Close")
        close_btn.setStyleSheet("""
            QPushButton {
                background: rgba(239, 68, 68, 0.8);
                border: none;
                border-radius: 14px;
                color: white;
                font-weight: bold;
                font-size: 10px;
            }
            QPushButton:hover {
                background: rgba(239, 68, 68, 1.0);
            }
        """)
        close_btn.clicked.connect(self.hide)
        
        options_layout.addWidget(main_btn)
        options_layout.addWidget(close_btn)
        
        layout.addLayout(options_layout)
        
    def on_copy_toggled(self, checked):
        """Handle copy option toggle."""
        self.copy_enabled = checked
        
    def on_paste_toggled(self, checked):
        """Handle paste option toggle."""
        self.paste_enabled = checked
        
    def update_status(self, status, is_recording=False):
        """Update status display."""
        self.is_recording = is_recording
        self.status_label.setText(status)
        
        if is_recording:
            self.status_dot.setStyleSheet("color: #ef4444;")  # Red
        else:
            self.status_dot.setStyleSheet("color: #10b981;")  # Green
            
        # Show window when recording starts
        if is_recording and not self.isVisible():
            self.show()
            self.raise_()
            self.activateWindow()
            
    def update_voice_level(self, level):
        """Update voice level display."""
        self.voice_level.set_level(level)
        
        # Ensure window is visible during recording
        if level > 0 and not self.isVisible():
            self.show()
            self.raise_()
            
    def get_copy_enabled(self):
        """Get copy option state."""
        return self.copy_enabled
        
    def get_paste_enabled(self):
        """Get paste option state."""
        return self.paste_enabled
        
    def make_draggable(self):
        """Make window draggable."""
        self.drag_start_position = None
        
    def mousePressEvent(self, event):
        """Handle mouse press for dragging."""
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.globalPos() - self.frameGeometry().topLeft()
            
    def mouseMoveEvent(self, event):
        """Handle mouse move for dragging."""
        if event.buttons() == Qt.LeftButton and self.drag_start_position:
            new_pos = event.globalPos() - self.drag_start_position
            self.move(new_pos)
            
    def mouseReleaseEvent(self, event):
        """Handle mouse release - save position."""
        if event.button() == Qt.LeftButton:
            self.save_position()
            
    def save_position(self):
        """Save window position."""
        self.settings.setValue("status_window_pos", self.pos())
        
    def load_position(self):
        """Load window position or center if first time."""
        pos = self.settings.value("status_window_pos")
        if pos:
            self.move(pos)
        else:
            # Center on screen for first time
            screen = QApplication.primaryScreen().geometry()
            x = (screen.width() - self.width()) // 2
            y = (screen.height() - self.height()) // 2
            self.move(x, y)
            
    def showEvent(self, event):
        """Handle show event."""
        super().showEvent(event)
        self.raise_()
        self.activateWindow()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = SWWStatusWindow()
    window.show()
    
    # Test voice level
    import random
    timer = QTimer()
    def update_level():
        level = random.randint(0, 100)
        window.update_voice_level(level)
        window.update_status(f"Recording... {level}%", True)
    
    timer.timeout.connect(update_level)
    timer.start(100)
    
    sys.exit(app.exec_())
