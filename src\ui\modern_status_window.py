import sys
import os
import json
import math
from datetime import datetime
from PyQt5.QtCore import (Qt, pyqtSignal, pyqtSlot, QTimer, QPropertyAnimation, 
                         QEasingCurve, QRect, QPoint, QParallelAnimationGroup)
from PyQt5.QtGui import (QFont, QPixmap, QIcon, QPainter, QBrush, QColor, QPen, 
                        QLinearGradient, QRadialGradient, QFontMetrics)
from PyQt5.QtWidgets import (QApplication, QLabel, QHBoxLayout, QVBoxLayout, QWidget, 
                           QPushButton, QScrollArea, QFrame, QMessageBox, QFileDialog,
                           QGraphicsDropShadowEffect, QSizePolicy)

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from utils import ConfigManager


class ModernVoiceMeter(QWidget):
    """Horizontal voice level meter like in the reference image."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(200, 30)
        self.levels = [0] * 20  # More bars for smoother horizontal display
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_animation)
        self.timer.start(50)

    def set_level(self, level):
        """Set audio level with CORRECT color mapping: Green=Low, Red=High."""
        target_bars = int((level / 100.0) * len(self.levels))

        for i in range(len(self.levels)):
            if i < target_bars:
                self.levels[i] = min(100, level)
            else:
                self.levels[i] = max(0, self.levels[i] - 15)  # Quick decay

    def update_animation(self):
        """Smooth animation update."""
        for i in range(len(self.levels)):
            if self.levels[i] > 0:
                self.levels[i] = max(0, self.levels[i] - 3)
        self.update()

    def paintEvent(self, event):
        """Paint horizontal voice bars with CORRECT colors."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        bar_width = 3
        bar_spacing = 1
        total_width = len(self.levels) * (bar_width + bar_spacing)
        start_x = (self.width() - total_width) // 2

        for i, level in enumerate(self.levels):
            if level > 5:  # Only draw visible bars
                x = start_x + i * (bar_width + bar_spacing)
                bar_height = int((level / 100.0) * (self.height() - 6))
                y = (self.height() - bar_height) // 2

                # FIXED COLOR LOGIC: Green for low levels, Red for high levels
                if i < len(self.levels) * 0.3:  # First 30% - Green (low levels)
                    color = QColor(34, 197, 94)  # Green
                elif i < len(self.levels) * 0.7:  # Middle 40% - Yellow/Orange
                    color = QColor(251, 191, 36)  # Yellow
                else:  # Last 30% - Red (high levels)
                    color = QColor(239, 68, 68)  # Red

                painter.setBrush(QBrush(color))
                painter.setPen(Qt.NoPen)
                painter.drawRoundedRect(x, y, bar_width, bar_height, 1, 1)


class ModernHistoryItem(QFrame):
    """Modern recording history item with sleek design."""
    
    copy_requested = pyqtSignal(str)
    view_requested = pyqtSignal(str, str)
    download_requested = pyqtSignal(str)
    retranscribe_requested = pyqtSignal(str)
    
    def __init__(self, recording_data, parent=None):
        super().__init__(parent)
        self.recording_data = recording_data
        self.init_modern_ui()
        
    def init_modern_ui(self):
        """Initialize modern UI for history item."""
        self.setFrameStyle(QFrame.NoFrame)
        self.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(40, 44, 52, 0.95),
                    stop:1 rgba(50, 54, 62, 0.95));
                border: 1px solid rgba(100, 100, 100, 0.3);
                border-radius: 12px;
                margin: 4px;
                padding: 8px;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(60, 64, 72, 0.95),
                    stop:1 rgba(70, 74, 82, 0.95));
                border: 1px solid rgba(0, 150, 255, 0.5);
            }
            QPushButton {
                background: rgba(0, 150, 255, 0.8);
                border: none;
                border-radius: 6px;
                color: white;
                font-weight: bold;
                padding: 4px 8px;
            }
            QPushButton:hover {
                background: rgba(0, 170, 255, 1.0);
            }
            QPushButton:pressed {
                background: rgba(0, 130, 255, 0.9);
            }
        """)
        
        # Add shadow effect
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(10)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 2)
        self.setGraphicsEffect(shadow)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(6)
        
        # Header with timestamp
        header_layout = QHBoxLayout()
        timestamp = QLabel(self.recording_data.get('timestamp', 'Unknown'))
        timestamp.setFont(QFont('Segoe UI', 9))
        timestamp.setStyleSheet("color: rgba(200, 200, 200, 0.8);")
        header_layout.addWidget(timestamp)
        header_layout.addStretch()
        
        # Transcription preview with better formatting
        transcription = self.recording_data.get('transcription', 'No transcription')
        preview = transcription[:60] + "..." if len(transcription) > 60 else transcription
        text_label = QLabel(preview)
        text_label.setFont(QFont('Segoe UI', 10))
        text_label.setStyleSheet("color: white; line-height: 1.4;")
        text_label.setWordWrap(True)
        
        # Modern action buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(6)
        
        buttons = [
            ("📋", lambda: self.copy_requested.emit(transcription)),
            ("👁", lambda: self.view_requested.emit(transcription, self.recording_data.get('timestamp', ''))),
            ("💾", lambda: self.download_requested.emit(self.recording_data.get('audio_path', ''))),
            ("🔄", lambda: self.retranscribe_requested.emit(self.recording_data.get('audio_path', '')))
        ]
        
        for icon, callback in buttons:
            btn = QPushButton(icon)
            btn.setFixedSize(28, 28)
            btn.clicked.connect(callback)
            button_layout.addWidget(btn)
            
        button_layout.addStretch()
        
        layout.addLayout(header_layout)
        layout.addWidget(text_label)
        layout.addLayout(button_layout)


class ModernStatusWindow(QWidget):
    """Ultra-modern sliding status window with click-to-open behavior."""

    statusSignal = pyqtSignal(str)
    closeSignal = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.is_hidden = True
        self.is_recording = False
        self.recording_history = []
        self.history_file = os.path.join('src', 'recording_history.json')

        self.init_modern_ui()
        self.load_history()
        self.setup_animations()
        self.create_arrow_indicator()
        self.position_window()

        # No auto-hide timer - user controls open/close
        
    def init_modern_ui(self):
        """Initialize ultra-modern UI."""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.setFixedSize(350, 500)
        
        # Main container with modern styling
        self.main_container = QWidget()
        self.main_container.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(20, 24, 32, 0.95),
                    stop:1 rgba(30, 34, 42, 0.95));
                border: 1px solid rgba(100, 100, 100, 0.3);
                border-radius: 20px;
            }
        """)
        
        # Add modern shadow
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(25)
        shadow.setColor(QColor(0, 0, 0, 120))
        shadow.setOffset(0, 5)
        self.main_container.setGraphicsEffect(shadow)
        
        main_layout = QVBoxLayout(self.main_container)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # Status section with modern icons
        self.create_status_section(main_layout)
        
        # Voice meter
        self.voice_meter = ModernVoiceMeter()
        main_layout.addWidget(self.voice_meter)
        
        # History section
        self.create_history_section(main_layout)

        # Add close button
        self.create_close_button(main_layout)

        # Set main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.main_container)
        
    def create_status_section(self, layout):
        """Create modern status section."""
        status_layout = QHBoxLayout()
        
        # Modern status indicator
        self.status_indicator = QLabel("●")
        self.status_indicator.setFont(QFont('Segoe UI', 16))
        self.status_indicator.setStyleSheet("color: #00ff88;")
        
        self.status_label = QLabel('Ready')
        self.status_label.setFont(QFont('Segoe UI', 12, QFont.Bold))
        self.status_label.setStyleSheet("color: white;")
        
        status_layout.addWidget(self.status_indicator)
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        layout.addLayout(status_layout)
        
    def create_history_section(self, layout):
        """Create modern history section."""
        history_label = QLabel("Recent Recordings")
        history_label.setFont(QFont('Segoe UI', 11, QFont.Bold))
        history_label.setStyleSheet("color: rgba(255, 255, 255, 0.9); margin-bottom: 5px;")
        
        self.history_scroll = QScrollArea()
        self.history_scroll.setWidgetResizable(True)
        self.history_scroll.setMaximumHeight(280)
        self.history_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(100, 100, 100, 0.3);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: rgba(0, 150, 255, 0.8);
                border-radius: 4px;
            }
        """)
        
        self.history_widget = QWidget()
        self.history_layout = QVBoxLayout(self.history_widget)
        self.history_layout.setContentsMargins(0, 0, 0, 0)
        self.history_layout.setSpacing(6)
        self.history_scroll.setWidget(self.history_widget)
        
        layout.addWidget(history_label)
        layout.addWidget(self.history_scroll)

        self.refresh_history_display()

    def create_close_button(self, layout):
        """Create close button."""
        close_layout = QHBoxLayout()
        close_layout.addStretch()

        close_btn = QPushButton("✕")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet("""
            QPushButton {
                background: rgba(255, 100, 100, 0.8);
                border: none;
                border-radius: 15px;
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background: rgba(255, 120, 120, 1.0);
            }
        """)
        close_btn.clicked.connect(self.slide_out)

        close_layout.addWidget(close_btn)
        layout.addLayout(close_layout)

    def create_arrow_indicator(self):
        """Create arrow indicator that shows when window is hidden."""
        self.arrow_indicator = QWidget()
        self.arrow_indicator.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.arrow_indicator.setAttribute(Qt.WA_TranslucentBackground, True)
        self.arrow_indicator.setFixedSize(30, 60)

        # Style the arrow
        self.arrow_indicator.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(0, 150, 255, 0.9),
                    stop:1 rgba(0, 100, 200, 0.9));
                border-radius: 15px 0px 0px 15px;
            }
        """)

        # Add arrow text
        arrow_layout = QVBoxLayout(self.arrow_indicator)
        arrow_layout.setContentsMargins(0, 0, 0, 0)
        arrow_label = QLabel("◀")
        arrow_label.setFont(QFont('Segoe UI', 14))
        arrow_label.setStyleSheet("color: white;")
        arrow_label.setAlignment(Qt.AlignCenter)
        arrow_layout.addWidget(arrow_label)

        # Make arrow clickable
        self.arrow_indicator.mousePressEvent = lambda event: self.slide_in()

        # Add glow effect to arrow
        arrow_shadow = QGraphicsDropShadowEffect()
        arrow_shadow.setBlurRadius(15)
        arrow_shadow.setColor(QColor(0, 150, 255, 150))
        arrow_shadow.setOffset(0, 0)
        self.arrow_indicator.setGraphicsEffect(arrow_shadow)
        
    def setup_animations(self):
        """Setup smooth slide animations."""
        self.slide_animation = QPropertyAnimation(self, b"pos")
        self.slide_animation.setDuration(300)
        self.slide_animation.setEasingCurve(QEasingCurve.OutCubic)
        
    def position_window(self):
        """Position window completely off-screen initially."""
        screen = QApplication.primaryScreen().geometry()
        self.hidden_x = screen.width()  # Completely hidden
        self.visible_x = screen.width() - self.width() - 10
        self.y_pos = screen.height() - self.height() - 100

        # Position main window off-screen
        self.move(self.hidden_x, self.y_pos)
        self.show()

        # Position arrow indicator
        arrow_x = screen.width() - self.arrow_indicator.width()
        arrow_y = screen.height() - self.height() // 2 - self.arrow_indicator.height() // 2
        self.arrow_indicator.move(arrow_x, arrow_y)
        self.arrow_indicator.show()

    def enterEvent(self, event):
        """Override - no auto slide in on hover."""
        super().enterEvent(event)

    def leaveEvent(self, event):
        """Override - no auto hide."""
        super().leaveEvent(event)
        
    def slide_in(self):
        """Slide window into view and hide arrow."""
        if self.is_hidden:
            self.is_hidden = False
            self.arrow_indicator.hide()  # Hide arrow when panel is open
            self.slide_animation.setStartValue(QPoint(self.hidden_x, self.y_pos))
            self.slide_animation.setEndValue(QPoint(self.visible_x, self.y_pos))
            self.slide_animation.start()

    def slide_out(self):
        """Slide window out of view and show arrow."""
        if not self.is_hidden:
            self.is_hidden = True
            self.slide_animation.setStartValue(QPoint(self.visible_x, self.y_pos))
            self.slide_animation.setEndValue(QPoint(self.hidden_x, self.y_pos))
            self.slide_animation.finished.connect(self.show_arrow_after_hide)
            self.slide_animation.start()

    def show_arrow_after_hide(self):
        """Show arrow after slide out animation completes."""
        if self.is_hidden:
            self.arrow_indicator.show()
        self.slide_animation.finished.disconnect()  # Disconnect to avoid multiple connections

    @pyqtSlot(str)
    def update_status(self, status):
        """Update status with modern styling."""
        if status == 'recording':
            self.status_indicator.setStyleSheet("color: #ff4444;")
            self.status_label.setText('Recording...')
            self.is_recording = True
            self.slide_in()  # Auto-open when recording starts
        elif status == 'transcribing':
            self.status_indicator.setStyleSheet("color: #ffaa00;")
            self.status_label.setText('Transcribing...')
            self.is_recording = False
        elif status in ('idle', 'error', 'cancel'):
            self.status_indicator.setStyleSheet("color: #00ff88;")
            self.status_label.setText('Ready')
            self.is_recording = False
            # Don't auto-hide - user controls with close button

    def update_voice_level(self, level):
        """Update voice level meter."""
        if hasattr(self, 'voice_meter'):
            self.voice_meter.set_level(level)

    def add_recording_to_history(self, transcription, audio_path=None):
        """Add recording to history."""
        recording_data = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'transcription': transcription,
            'audio_path': audio_path or ''
        }

        self.recording_history.insert(0, recording_data)

        # Limit to 20 items
        if len(self.recording_history) > 20:
            self.recording_history = self.recording_history[:20]

        self.save_history()
        self.refresh_history_display()

    def refresh_history_display(self):
        """Refresh history with modern items."""
        # Clear existing items
        for i in reversed(range(self.history_layout.count())):
            child = self.history_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # Add modern history items
        for recording_data in self.recording_history[:8]:  # Show last 8
            item = ModernHistoryItem(recording_data)
            item.copy_requested.connect(self.copy_text)
            item.view_requested.connect(self.view_transcription)
            item.download_requested.connect(self.download_recording)
            item.retranscribe_requested.connect(self.retranscribe_recording)
            self.history_layout.addWidget(item)

        self.history_layout.addStretch()

    def copy_text(self, text):
        """Copy text to clipboard."""
        clipboard = QApplication.clipboard()
        clipboard.setText(text)

    def view_transcription(self, text, timestamp):
        """Show transcription in modern dialog."""
        dialog = QMessageBox(self)
        dialog.setWindowTitle(f"Transcription - {timestamp}")
        dialog.setText(text)
        dialog.setStyleSheet("""
            QMessageBox {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(40, 44, 52, 0.95),
                    stop:1 rgba(50, 54, 62, 0.95));
                color: white;
                border-radius: 10px;
            }
            QPushButton {
                background: rgba(0, 150, 255, 0.8);
                border: none;
                border-radius: 6px;
                color: white;
                padding: 8px 16px;
                font-weight: bold;
            }
        """)
        dialog.exec_()

    def download_recording(self, audio_path):
        """Download recording with modern file dialog."""
        if audio_path and os.path.exists(audio_path):
            try:
                file_name = os.path.basename(audio_path)
                save_path, _ = QFileDialog.getSaveFileName(
                    self,
                    "Save Recording",
                    file_name,
                    "Audio Files (*.wav *.mp3 *.m4a);;All Files (*)"
                )

                if save_path:
                    import shutil
                    shutil.copy2(audio_path, save_path)

            except Exception as e:
                print(f"Download error: {e}")

    def retranscribe_recording(self, audio_path):
        """Re-transcribe with modern feedback."""
        if audio_path and os.path.exists(audio_path):
            try:
                from transcription import transcribe, create_local_model
                import numpy as np
                import soundfile as sf

                # Show processing status
                self.status_indicator.setStyleSheet("color: #ffaa00;")
                self.status_label.setText('Re-transcribing...')

                # Load and transcribe
                audio_data, _ = sf.read(audio_path)
                if audio_data.dtype != np.int16:
                    audio_data = (audio_data * 32767).astype(np.int16)

                model_options = ConfigManager.get_config_section('model_options')
                local_model = create_local_model() if not model_options.get('use_api') else None
                result = transcribe(audio_data, local_model)

                if result:
                    self.add_recording_to_history(result, audio_path)

                # Reset status
                self.status_indicator.setStyleSheet("color: #00ff88;")
                self.status_label.setText('Ready')

            except Exception as e:
                print(f"Re-transcribe error: {e}")

    def load_history(self):
        """Load history from file."""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.recording_history = json.load(f)
        except Exception as e:
            print(f"Error loading history: {e}")
            self.recording_history = []

    def save_history(self):
        """Save history to file."""
        try:
            os.makedirs(os.path.dirname(self.history_file), exist_ok=True)
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.recording_history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving history: {e}")

    def closeEvent(self, event):
        """Handle close event."""
        self.closeSignal.emit()
        super().closeEvent(event)
