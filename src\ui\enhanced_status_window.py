import sys
import os
import json
import time
import shutil
from datetime import datetime
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot, QTimer, QPropertyAnimation, QEasingCurve, QRect, QPoint
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPainter, QBrush, QColor, QPen, QLinearGradient
from PyQt5.QtWidgets import (QApplication, QLabel, QHBoxLayout, QVBoxLayout, QWidget,
                           QPushButton, QScrollArea, QFrame, QTextEdit, QMessageBox, QFileDialog)

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from ui.base_window import BaseWindow
from utils import ConfigManager


class VoiceLevelMeter(QWidget):
    """Animated voice level meter with bars."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(200, 60)
        self.levels = [0] * 10  # 10 bars
        self.max_level = 100
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_animation)
        self.timer.start(50)  # Update every 50ms for smooth animation
        
    def set_level(self, level):
        """Set the current audio level (0-100)."""
        # Distribute level across bars with some randomness for visual appeal
        target_bars = int((level / 100.0) * len(self.levels))
        
        for i in range(len(self.levels)):
            if i < target_bars:
                self.levels[i] = min(100, level + (i * 5))  # Slight variation
            else:
                self.levels[i] = max(0, self.levels[i] - 10)  # Decay
                
    def update_animation(self):
        """Update the animation and repaint."""
        self.update()
        
    def paintEvent(self, event):
        """Paint the voice level bars."""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        bar_width = (self.width() - 20) // len(self.levels)
        bar_spacing = 2
        
        for i, level in enumerate(self.levels):
            x = 10 + i * (bar_width + bar_spacing)
            bar_height = int((level / 100.0) * (self.height() - 10))
            y = self.height() - bar_height - 5
            
            # Create gradient based on level
            gradient = QLinearGradient(0, y, 0, y + bar_height)
            if level > 70:
                gradient.setColorAt(0, QColor(255, 100, 100))  # Red
                gradient.setColorAt(1, QColor(255, 200, 100))  # Orange
            elif level > 40:
                gradient.setColorAt(0, QColor(255, 200, 100))  # Orange
                gradient.setColorAt(1, QColor(100, 255, 100))  # Green
            else:
                gradient.setColorAt(0, QColor(100, 255, 100))  # Green
                gradient.setColorAt(1, QColor(100, 200, 255))  # Blue
                
            painter.setBrush(QBrush(gradient))
            painter.setPen(Qt.NoPen)
            painter.drawRoundedRect(x, y, bar_width - bar_spacing, bar_height, 2, 2)


class RecordingHistoryItem(QFrame):
    """Individual recording history item widget."""
    
    copy_requested = pyqtSignal(str)
    view_requested = pyqtSignal(str, str)
    download_requested = pyqtSignal(str)
    retranscribe_requested = pyqtSignal(str)
    
    def __init__(self, recording_data, parent=None):
        super().__init__(parent)
        self.recording_data = recording_data
        self.init_ui()
        
    def init_ui(self):
        """Initialize the UI for the history item."""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.9);
                border: 1px solid #ddd;
                border-radius: 5px;
                margin: 2px;
                padding: 5px;
            }
            QFrame:hover {
                background-color: rgba(240, 240, 255, 0.9);
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        
        # Header with timestamp
        header_layout = QHBoxLayout()
        timestamp = QLabel(self.recording_data.get('timestamp', 'Unknown'))
        timestamp.setFont(QFont('Segoe UI', 8))
        timestamp.setStyleSheet("color: #666;")
        header_layout.addWidget(timestamp)
        header_layout.addStretch()
        
        # Transcription preview
        transcription = self.recording_data.get('transcription', 'No transcription')
        preview = transcription[:50] + "..." if len(transcription) > 50 else transcription
        text_label = QLabel(preview)
        text_label.setFont(QFont('Segoe UI', 9))
        text_label.setWordWrap(True)
        
        # Action buttons
        button_layout = QHBoxLayout()
        
        copy_btn = QPushButton("Copy")
        copy_btn.setFixedSize(50, 25)
        copy_btn.clicked.connect(lambda: self.copy_requested.emit(transcription))
        
        view_btn = QPushButton("View")
        view_btn.setFixedSize(50, 25)
        view_btn.clicked.connect(lambda: self.view_requested.emit(transcription, self.recording_data.get('timestamp', '')))
        
        download_btn = QPushButton("Download")
        download_btn.setFixedSize(70, 25)
        download_btn.clicked.connect(lambda: self.download_requested.emit(self.recording_data.get('audio_path', '')))
        
        retry_btn = QPushButton("Retry")
        retry_btn.setFixedSize(50, 25)
        retry_btn.clicked.connect(lambda: self.retranscribe_requested.emit(self.recording_data.get('audio_path', '')))
        
        button_layout.addWidget(copy_btn)
        button_layout.addWidget(view_btn)
        button_layout.addWidget(download_btn)
        button_layout.addWidget(retry_btn)
        button_layout.addStretch()
        
        layout.addLayout(header_layout)
        layout.addWidget(text_label)
        layout.addLayout(button_layout)


class EnhancedStatusWindow(BaseWindow):
    """Enhanced status window with voice level visualization and recording history."""
    
    statusSignal = pyqtSignal(str)
    closeSignal = pyqtSignal()
    
    def __init__(self):
        super().__init__('WhisperWriter Enhanced Status', 300, 400)
        self.is_minimized = False
        self.is_recording = False
        self.recording_history = []
        self.history_file = os.path.join('src', 'recording_history.json')
        
        self.init_enhanced_ui()
        self.load_history()
        self.statusSignal.connect(self.update_status)
        
        # Setup corner positioning
        self.setup_corner_position()
        
    def init_enhanced_ui(self):
        """Initialize the enhanced UI."""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        
        # Main container
        self.main_container = QWidget()
        main_layout = QVBoxLayout(self.main_container)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Status section
        status_layout = QHBoxLayout()
        
        # Icon
        self.icon_label = QLabel()
        self.icon_label.setFixedSize(32, 32)
        microphone_path = os.path.join('assets', 'microphone.png')
        pencil_path = os.path.join('assets', 'pencil.png')
        self.microphone_pixmap = QPixmap(microphone_path).scaled(32, 32, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        self.pencil_pixmap = QPixmap(pencil_path).scaled(32, 32, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        self.icon_label.setPixmap(self.microphone_pixmap)
        
        # Status text
        self.status_label = QLabel('Ready')
        self.status_label.setFont(QFont('Segoe UI', 10))
        
        status_layout.addWidget(self.icon_label)
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        # Voice level meter
        self.voice_meter = VoiceLevelMeter()
        
        # History section
        history_label = QLabel("Recording History")
        history_label.setFont(QFont('Segoe UI', 9, QFont.Bold))
        
        self.history_scroll = QScrollArea()
        self.history_scroll.setWidgetResizable(True)
        self.history_scroll.setMaximumHeight(200)
        
        self.history_widget = QWidget()
        self.history_layout = QVBoxLayout(self.history_widget)
        self.history_layout.setContentsMargins(0, 0, 0, 0)
        self.history_scroll.setWidget(self.history_widget)
        
        # Add to main layout
        main_layout.addLayout(status_layout)
        main_layout.addWidget(self.voice_meter)
        main_layout.addWidget(history_label)
        main_layout.addWidget(self.history_scroll)

        self.main_layout.addWidget(self.main_container)

        # Initialize history display
        self.refresh_history_display()
        
    def setup_corner_position(self):
        """Setup the window to stick to bottom-right corner."""
        screen = QApplication.primaryScreen()
        screen_geometry = screen.geometry()
        
        # Position in bottom-right corner
        x = screen_geometry.width() - self.width() - 20
        y = screen_geometry.height() - self.height() - 100
        
        self.move(x, y)
        
    def enterEvent(self, event):
        """Show full window on hover."""
        if self.is_minimized:
            self.expand_window()
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """Minimize window when mouse leaves."""
        if not self.is_recording:
            QTimer.singleShot(1000, self.minimize_window)  # Delay to prevent flickering
        super().leaveEvent(event)
        
    def minimize_window(self):
        """Minimize the window to show only essential info."""
        if not self.underMouse() and not self.is_recording:
            self.is_minimized = True
            self.setFixedSize(100, 80)
            self.setup_corner_position()
            
    def expand_window(self):
        """Expand the window to show full interface."""
        self.is_minimized = False
        self.setFixedSize(300, 400)
        self.setup_corner_position()

    @pyqtSlot(str)
    def update_status(self, status):
        """Update the status window based on the given status."""
        if status == 'recording':
            self.icon_label.setPixmap(self.microphone_pixmap)
            self.status_label.setText('Recording...')
            self.is_recording = True
            self.expand_window()
            self.show()
        elif status == 'transcribing':
            self.icon_label.setPixmap(self.pencil_pixmap)
            self.status_label.setText('Transcribing...')
            self.is_recording = False
        elif status in ('idle', 'error', 'cancel'):
            self.is_recording = False
            if not self.underMouse():
                QTimer.singleShot(2000, self.minimize_window)

    def update_voice_level(self, level):
        """Update the voice level meter."""
        if hasattr(self, 'voice_meter'):
            self.voice_meter.set_level(level)

    def add_recording_to_history(self, transcription, audio_path=None):
        """Add a new recording to the history."""
        recording_data = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'transcription': transcription,
            'audio_path': audio_path or ''
        }

        self.recording_history.insert(0, recording_data)  # Add to beginning

        # Limit history to 50 items
        if len(self.recording_history) > 50:
            self.recording_history = self.recording_history[:50]

        self.save_history()
        self.refresh_history_display()

    def refresh_history_display(self):
        """Refresh the history display."""
        # Clear existing items
        for i in reversed(range(self.history_layout.count())):
            child = self.history_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # Add history items
        for recording_data in self.recording_history[:10]:  # Show last 10
            item = RecordingHistoryItem(recording_data)
            item.copy_requested.connect(self.copy_text)
            item.view_requested.connect(self.view_transcription)
            item.download_requested.connect(self.download_recording)
            item.retranscribe_requested.connect(self.retranscribe_recording)
            self.history_layout.addWidget(item)

        self.history_layout.addStretch()

    def copy_text(self, text):
        """Copy text to clipboard."""
        clipboard = QApplication.clipboard()
        clipboard.setText(text)

    def view_transcription(self, text, timestamp):
        """Show full transcription in a dialog."""
        dialog = QMessageBox(self)
        dialog.setWindowTitle(f"Transcription - {timestamp}")
        dialog.setText(text)
        dialog.setStandardButtons(QMessageBox.Ok)
        dialog.exec_()

    def download_recording(self, audio_path):
        """Download/save the original recording."""
        if audio_path and os.path.exists(audio_path):
            try:
                # Open file dialog to choose save location
                file_name = os.path.basename(audio_path)
                save_path, _ = QFileDialog.getSaveFileName(
                    self,
                    "Save Recording",
                    file_name,
                    "Audio Files (*.wav *.mp3 *.m4a);;All Files (*)"
                )

                if save_path:
                    shutil.copy2(audio_path, save_path)
                    QMessageBox.information(self, "Download", f"Recording saved to: {save_path}")

            except Exception as e:
                QMessageBox.warning(self, "Download Error", f"Failed to save recording: {str(e)}")
        else:
            QMessageBox.warning(self, "Download", "Audio file not available")

    def retranscribe_recording(self, audio_path):
        """Re-transcribe the recording."""
        if audio_path and os.path.exists(audio_path):
            try:
                # Import transcription module
                from transcription import transcribe, create_local_model
                import numpy as np
                import soundfile as sf

                # Load audio file
                audio_data, sample_rate = sf.read(audio_path)

                # Convert to int16 if needed
                if audio_data.dtype != np.int16:
                    audio_data = (audio_data * 32767).astype(np.int16)

                # Create model if needed
                model_options = ConfigManager.get_config_section('model_options')
                local_model = create_local_model() if not model_options.get('use_api') else None

                # Transcribe
                result = transcribe(audio_data, local_model)

                if result:
                    # Show result in dialog
                    dialog = QMessageBox(self)
                    dialog.setWindowTitle("Re-transcription Result")
                    dialog.setText(f"New transcription:\n\n{result}")
                    dialog.setStandardButtons(QMessageBox.Ok)
                    dialog.exec_()

                    # Add to history
                    self.add_recording_to_history(result, audio_path)
                else:
                    QMessageBox.warning(self, "Re-transcribe", "Transcription failed or returned empty result")

            except Exception as e:
                QMessageBox.warning(self, "Re-transcribe Error", f"Failed to re-transcribe: {str(e)}")
        else:
            QMessageBox.warning(self, "Re-transcribe", "Audio file not available")

    def load_history(self):
        """Load recording history from file."""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.recording_history = json.load(f)
        except Exception as e:
            print(f"Error loading history: {e}")
            self.recording_history = []

    def save_history(self):
        """Save recording history to file."""
        try:
            os.makedirs(os.path.dirname(self.history_file), exist_ok=True)
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.recording_history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving history: {e}")

    def closeEvent(self, event):
        """Emit the close signal when the window is closed."""
        self.closeSignal.emit()
        super().closeEvent(event)
