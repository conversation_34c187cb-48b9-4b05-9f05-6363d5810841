import sys
import os
import json
from datetime import datetime
from PyQt5.QtCore import (Qt, pyqtSignal, QTimer, QSize)
from PyQt5.QtGui import (QFont, QPainter, QBrush, QColor, QPen, QLinearGradient, 
                        QFontMetrics, QIcon, QPixmap)
from PyQt5.QtWidgets import (QApplication, QLabel, QHBoxLayout, QVBoxLayout, QWidget, 
                           QPushButton, QScrollArea, QFrame, QMainWindow, QLineEdit,
                           QListWidget, QListWidgetItem, QTextEdit, QSplitter,
                           QGraphicsDropShadowEffect, QMessageBox)

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


class SidebarButton(QPushButton):
    """Custom sidebar button with modern styling."""
    
    def __init__(self, text, icon_text="", parent=None):
        super().__init__(parent)
        self.setText(text)
        self.icon_text = icon_text
        self.setFixedHeight(45)
        self.setFont(QFont('Segoe UI', 10))
        
        self.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                color: rgba(255, 255, 255, 0.7);
                text-align: left;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: 500;
            }
            QPushButton:hover {
                background: rgba(59, 130, 246, 0.1);
                color: rgba(255, 255, 255, 0.9);
            }
            QPushButton:checked {
                background: rgba(59, 130, 246, 0.2);
                color: #3b82f6;
                font-weight: 600;
            }
        """)
        
        self.setCheckable(True)


class RecordingCard(QWidget):
    """Individual recording card in the history list."""
    
    copy_requested = pyqtSignal(str)
    delete_requested = pyqtSignal(int)
    
    def __init__(self, recording_data, parent=None):
        super().__init__(parent)
        self.recording_data = recording_data
        self.init_ui()
        
    def init_ui(self):
        """Initialize the recording card UI."""
        self.setFixedHeight(120)
        self.setStyleSheet("""
            QWidget {
                background: rgba(40, 40, 45, 0.8);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                margin: 5px;
            }
            QWidget:hover {
                background: rgba(45, 45, 50, 0.9);
                border: 1px solid rgba(59, 130, 246, 0.3);
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 12, 15, 12)
        layout.setSpacing(8)
        
        # Header with timestamp and duration
        header_layout = QHBoxLayout()
        
        # Timestamp
        timestamp = datetime.fromisoformat(self.recording_data['timestamp'])
        time_label = QLabel(timestamp.strftime("%b %d, %Y at %I:%M %p"))
        time_label.setFont(QFont('Segoe UI', 9))
        time_label.setStyleSheet("color: rgba(255, 255, 255, 0.6);")
        
        # Duration
        duration_label = QLabel(f"{self.recording_data.get('duration', 0):.1f}s")
        duration_label.setFont(QFont('Segoe UI', 9, QFont.Bold))
        duration_label.setStyleSheet("color: #3b82f6;")
        
        header_layout.addWidget(time_label)
        header_layout.addStretch()
        header_layout.addWidget(duration_label)
        
        # Text preview
        text_preview = self.recording_data['text'][:100]
        if len(self.recording_data['text']) > 100:
            text_preview += "..."
            
        text_label = QLabel(text_preview)
        text_label.setFont(QFont('Segoe UI', 10))
        text_label.setStyleSheet("color: white;")
        text_label.setWordWrap(True)
        text_label.setMaximumHeight(40)
        
        # Action buttons
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)
        
        copy_btn = QPushButton("Copy")
        copy_btn.setFixedSize(60, 25)
        copy_btn.setStyleSheet(self.get_action_button_style("#10b981"))
        copy_btn.clicked.connect(lambda: self.copy_requested.emit(self.recording_data['text']))
        
        delete_btn = QPushButton("Delete")
        delete_btn.setFixedSize(60, 25)
        delete_btn.setStyleSheet(self.get_action_button_style("#ef4444"))
        delete_btn.clicked.connect(lambda: self.delete_requested.emit(self.recording_data['id']))
        
        button_layout.addWidget(copy_btn)
        button_layout.addWidget(delete_btn)
        button_layout.addStretch()
        
        layout.addLayout(header_layout)
        layout.addWidget(text_label)
        layout.addLayout(button_layout)
        
    def get_action_button_style(self, color):
        """Get action button styling."""
        return f"""
            QPushButton {{
                background: {color};
                border: none;
                border-radius: 4px;
                color: white;
                font-size: 9px;
                font-weight: 500;
            }}
            QPushButton:hover {{
                background: {color}dd;
            }}
            QPushButton:pressed {{
                background: {color}bb;
            }}
        """


class HistoryWindow(QMainWindow):
    """Modern history window like the reference image."""
    
    def __init__(self, recording_history, parent=None):
        super().__init__(parent)
        self.recording_history = recording_history
        self.current_recording = None
        
        self.init_ui()
        self.load_recordings()
        
    def init_ui(self):
        """Initialize the modern UI like reference image."""
        self.setWindowTitle("Whisper Writer - History")
        self.setGeometry(100, 100, 1200, 800)
        
        # Set dark theme
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(25, 25, 30, 1.0),
                    stop:1 rgba(20, 20, 25, 1.0));
            }
        """)
        
        # Central widget with splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Left sidebar
        self.create_sidebar(splitter)
        
        # Main content area
        self.create_main_content(splitter)
        
        # Right detail panel
        self.create_detail_panel(splitter)
        
        # Set splitter proportions
        splitter.setSizes([250, 500, 450])
        splitter.setChildrenCollapsible(False)
        
        main_layout.addWidget(splitter)
        
    def create_sidebar(self, parent):
        """Create left sidebar like reference."""
        sidebar = QWidget()
        sidebar.setFixedWidth(250)
        sidebar.setStyleSheet("""
            QWidget {
                background: rgba(30, 30, 35, 0.95);
                border-right: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(0, 20, 0, 20)
        layout.setSpacing(5)
        
        # Title
        title = QLabel("Whisper Writer")
        title.setFont(QFont('Segoe UI', 14, QFont.Bold))
        title.setStyleSheet("color: white; padding: 0 20px; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # Navigation buttons
        self.history_btn = SidebarButton("📋 History", "📋")
        self.models_btn = SidebarButton("🤖 AI Models", "🤖")
        self.config_btn = SidebarButton("⚙️ Configuration", "⚙️")
        self.about_btn = SidebarButton("ℹ️ About", "ℹ️")
        
        # Set History as active by default
        self.history_btn.setChecked(True)
        
        layout.addWidget(self.history_btn)
        layout.addWidget(self.models_btn)
        layout.addWidget(self.config_btn)
        layout.addWidget(self.about_btn)
        
        layout.addStretch()
        
        # Version info
        version_label = QLabel("v1.0.0")
        version_label.setFont(QFont('Segoe UI', 9))
        version_label.setStyleSheet("color: rgba(255, 255, 255, 0.4); padding: 0 20px;")
        layout.addWidget(version_label)
        
        parent.addWidget(sidebar)
        
    def create_main_content(self, parent):
        """Create main content area with recording list."""
        content_widget = QWidget()
        content_widget.setStyleSheet("""
            QWidget {
                background: rgba(25, 25, 30, 1.0);
            }
        """)
        
        layout = QVBoxLayout(content_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Header
        header_layout = QHBoxLayout()
        
        title = QLabel("History")
        title.setFont(QFont('Segoe UI', 18, QFont.Bold))
        title.setStyleSheet("color: white;")
        
        # Search box
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("Search recordings...")
        self.search_box.setFixedHeight(35)
        self.search_box.setStyleSheet("""
            QLineEdit {
                background: rgba(40, 40, 45, 0.8);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                color: white;
                padding: 0 12px;
                font-size: 11px;
            }
            QLineEdit:focus {
                border: 1px solid #3b82f6;
            }
        """)
        
        header_layout.addWidget(title)
        header_layout.addStretch()
        header_layout.addWidget(self.search_box)
        
        # Recording list
        self.recording_list = QScrollArea()
        self.recording_list.setWidgetResizable(True)
        self.recording_list.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(40, 40, 45, 0.5);
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 4px;
            }
        """)
        
        self.recording_container = QWidget()
        self.recording_layout = QVBoxLayout(self.recording_container)
        self.recording_layout.setSpacing(10)
        self.recording_layout.setContentsMargins(0, 0, 0, 0)
        
        self.recording_list.setWidget(self.recording_container)
        
        layout.addLayout(header_layout)
        layout.addWidget(self.recording_list)
        
        parent.addWidget(content_widget)

    def create_detail_panel(self, parent):
        """Create right detail panel like reference."""
        detail_widget = QWidget()
        detail_widget.setStyleSheet("""
            QWidget {
                background: rgba(30, 30, 35, 0.95);
                border-left: 1px solid rgba(255, 255, 255, 0.1);
            }
        """)

        layout = QVBoxLayout(detail_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # Recording info section
        info_section = QWidget()
        info_layout = QVBoxLayout(info_section)
        info_layout.setSpacing(10)

        # Title
        self.detail_title = QLabel("Select a recording")
        self.detail_title.setFont(QFont('Segoe UI', 14, QFont.Bold))
        self.detail_title.setStyleSheet("color: white;")

        # Stats
        self.stats_widget = QWidget()
        self.create_stats_section()

        info_layout.addWidget(self.detail_title)
        info_layout.addWidget(self.stats_widget)

        # Transcript section
        transcript_label = QLabel("Transcript")
        transcript_label.setFont(QFont('Segoe UI', 12, QFont.Bold))
        transcript_label.setStyleSheet("color: white; margin-top: 10px;")

        self.transcript_text = QTextEdit()
        self.transcript_text.setStyleSheet("""
            QTextEdit {
                background: rgba(40, 40, 45, 0.8);
                border: 1px solid rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                color: white;
                padding: 12px;
                font-family: 'Segoe UI';
                font-size: 11px;
                line-height: 1.4;
            }
        """)
        self.transcript_text.setPlaceholderText("No recording selected")

        # Action buttons
        action_layout = QHBoxLayout()
        action_layout.setSpacing(10)

        self.copy_btn = QPushButton("Copy")
        self.copy_btn.setStyleSheet(self.get_primary_button_style("#10b981"))
        self.copy_btn.clicked.connect(self.copy_transcript)

        self.export_btn = QPushButton("Export")
        self.export_btn.setStyleSheet(self.get_primary_button_style("#3b82f6"))

        action_layout.addWidget(self.copy_btn)
        action_layout.addWidget(self.export_btn)
        action_layout.addStretch()

        layout.addWidget(info_section)
        layout.addWidget(transcript_label)
        layout.addWidget(self.transcript_text)
        layout.addLayout(action_layout)
        layout.addStretch()

        parent.addWidget(detail_widget)

    def create_stats_section(self):
        """Create stats section like reference."""
        layout = QVBoxLayout(self.stats_widget)
        layout.setSpacing(8)

        # Recording stats
        self.duration_label = QLabel("Duration: --")
        self.duration_label.setFont(QFont('Segoe UI', 10))
        self.duration_label.setStyleSheet("color: rgba(255, 255, 255, 0.7);")

        self.words_label = QLabel("Words: --")
        self.words_label.setFont(QFont('Segoe UI', 10))
        self.words_label.setStyleSheet("color: rgba(255, 255, 255, 0.7);")

        self.timestamp_label = QLabel("Created: --")
        self.timestamp_label.setFont(QFont('Segoe UI', 10))
        self.timestamp_label.setStyleSheet("color: rgba(255, 255, 255, 0.7);")

        layout.addWidget(self.duration_label)
        layout.addWidget(self.words_label)
        layout.addWidget(self.timestamp_label)

    def get_primary_button_style(self, color):
        """Get primary button styling."""
        return f"""
            QPushButton {{
                background: {color};
                border: none;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                padding: 10px 20px;
                font-size: 11px;
            }}
            QPushButton:hover {{
                background: {color}dd;
            }}
            QPushButton:pressed {{
                background: {color}bb;
            }}
        """

    def load_recordings(self):
        """Load recordings into the list."""
        # Clear existing items
        for i in reversed(range(self.recording_layout.count())):
            child = self.recording_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # Add recording cards
        for recording in self.recording_history:
            card = RecordingCard(recording)
            card.copy_requested.connect(self.copy_text)
            card.delete_requested.connect(self.delete_recording)
            card.mousePressEvent = lambda event, r=recording: self.select_recording(r)

            self.recording_layout.addWidget(card)

        # Add stretch at the end
        self.recording_layout.addStretch()

    def select_recording(self, recording):
        """Select and display recording details."""
        self.current_recording = recording

        # Update detail panel
        self.detail_title.setText("Recording Details")

        # Update stats
        timestamp = datetime.fromisoformat(recording['timestamp'])
        self.duration_label.setText(f"Duration: {recording.get('duration', 0):.1f}s")
        self.words_label.setText(f"Words: {len(recording['text'].split())}")
        self.timestamp_label.setText(f"Created: {timestamp.strftime('%b %d, %Y at %I:%M %p')}")

        # Update transcript
        self.transcript_text.setPlainText(recording['text'])

    def copy_text(self, text):
        """Copy text to clipboard."""
        clipboard = QApplication.clipboard()
        clipboard.setText(text)

        # Show confirmation (optional)
        QMessageBox.information(self, "Copied", "Text copied to clipboard!")

    def copy_transcript(self):
        """Copy current transcript to clipboard."""
        if self.current_recording:
            self.copy_text(self.current_recording['text'])

    def delete_recording(self, recording_id):
        """Delete a recording."""
        reply = QMessageBox.question(self, "Delete Recording",
                                   "Are you sure you want to delete this recording?",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            # Remove from history
            self.recording_history = [r for r in self.recording_history if r['id'] != recording_id]

            # Reload the list
            self.load_recordings()

            # Clear detail panel if this recording was selected
            if self.current_recording and self.current_recording['id'] == recording_id:
                self.current_recording = None
                self.detail_title.setText("Select a recording")
                self.transcript_text.setPlainText("")
                self.duration_label.setText("Duration: --")
                self.words_label.setText("Words: --")
                self.timestamp_label.setText("Created: --")


if __name__ == "__main__":
    app = QApplication(sys.argv)

    # Sample data for testing
    sample_history = [
        {
            'id': 1,
            'text': "Hey John, I'm excited about our meeting coming up later this week. I can't believe how much progress he's made on the project.",
            'duration': 11.87,
            'timestamp': '2023-12-26T20:30:00'
        },
        {
            'id': 2,
            'text': "The speaker was believes that Apple's success as a company...",
            'duration': 9.62,
            'timestamp': '2023-12-26T21:18:00'
        }
    ]

    window = HistoryWindow(sample_history)
    window.show()

    sys.exit(app.exec_())
